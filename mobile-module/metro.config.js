const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');
const { withNativeWind } = require("nativewind/metro");



/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */

const config = {
    resolver: {
        alias: {
            '@': path.resolve(__dirname, 'src'),
            'moti/skeleton': path.resolve(__dirname,'node_modules/moti/skeleton/react-native-linear-gradient'),
            "@global/css": path.resolve(__dirname, 'global.css'),
        },
        // unstable_enablePackageExports: false,

    }
};

module.exports = withNativeWind(mergeConfig(getDefaultConfig(__dirname), config),
 {input: "./global.css"});
