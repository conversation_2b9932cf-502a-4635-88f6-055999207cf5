module.exports = {
  size: {
    "3xs": "var(--font-size-3xs)",
    "2xs": "var(--font-size-2xs)",
    xs: "var(--font-size-xs)",
    sm: "var(--font-size-sm)",
    base: "var(--font-size-base)",
    lg: "var(--font-size-lg)",
    xl: "var(--font-size-xl)",
    "2xl": "var(--font-size-2xl)",
    "3xl": "var(--font-size-3xl)",
    "4xl": "var(--font-size-4xl)",
    "5xl": "var(--font-size-5xl)",
    "6xl": "var(--font-size-6xl)",
    "7xl": "var(--font-size-7xl)",
    "8xl": "var(--font-size-8xl)",
    "9xl": "var(--font-size-9xl)",
    "10xl": "var(--font-size-10xl)"
  },
  weight: {
    400: {
      regular: "var(--font-weight-400-regular)"
    },
    500: {
      medium: "var(--font-weight-500-medium)"
    },
    600: {
      semibold: "var(--font-weight-600-semibold)"
    },
    700: {
      bold: "var(--font-weight-700-bold)"
    },
    800: {
      extrabold: "var(--font-weight-800-extrabold)"
    }
  },
  lineheight: {
    100: "var(--font-lineheight-100)",
    105: "var(--font-lineheight-105)",
    110: "var(--font-lineheight-110)",
    115: "var(--font-lineheight-115)",
    125: "var(--font-lineheight-125)",
    150: "var(--font-lineheight-150)"
  },
  tracking: {
    tightest: "var(--font-tracking-tightest)",
    tighter: "var(--font-tracking-tighter)",
    tight: "var(--font-tracking-tight)",
    base: "var(--font-tracking-base)",
    wide: "var(--font-tracking-wide)",
    wider: "var(--font-tracking-wider)",
    widest: "var(--font-tracking-widest)"
  }
}