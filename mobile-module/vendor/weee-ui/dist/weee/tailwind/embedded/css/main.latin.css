/**
 * Do not edit directly
 * Generated on Mon, 18 Aug 2025 09:30:18 GMT
 */
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLDz8Z11lFc-K.woff2) format('woff2');
  unicode-range: u+0900-097f, u+1cd0-1cf6, u+1cf8-1cf9, u+200c-200d, u+20a8, u+20b9, u+25cc, u+a830-a839, u+a8e0-a8fb;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLDz8Z1JlFc-K.woff2) format('woff2');
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf, u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2) format('woff2');
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+2000-206f, u+2074, u+20ac, u+2122,
    u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 400;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiEyp8kv8JHgFVrJJbecmNE.woff2) format('woff2');
  unicode-range: u+0900-097f, u+1cd0-1cf6, u+1cf8-1cf9, u+200c-200d, u+20a8, u+20b9, u+25cc, u+a830-a839, u+a8e0-a8fb;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 400;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiEyp8kv8JHgFVrJJnecmNE.woff2) format('woff2');
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf, u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 400;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+2000-206f, u+2074, u+20ac, u+2122,
    u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 500;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLGT9Z11lFc-K.woff2) format('woff2');
  unicode-range: u+0900-097f, u+1cd0-1cf6, u+1cf8-1cf9, u+200c-200d, u+20a8, u+20b9, u+25cc, u+a830-a839, u+a8e0-a8fb;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 500;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLGT9Z1JlFc-K.woff2) format('woff2');
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf, u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 500;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2) format('woff2');
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+2000-206f, u+2074, u+20ac, u+2122,
    u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 600;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLEj6Z11lFc-K.woff2) format('woff2');
  unicode-range: u+0900-097f, u+1cd0-1cf6, u+1cf8-1cf9, u+200c-200d, u+20a8, u+20b9, u+25cc, u+a830-a839, u+a8e0-a8fb;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 600;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLEj6Z1JlFc-K.woff2) format('woff2');
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf, u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 600;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLEj6Z1xlFQ.woff2) format('woff2');
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+2000-206f, u+2074, u+20ac, u+2122,
    u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 700;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLCz7Z11lFc-K.woff2) format('woff2');
  unicode-range: u+0900-097f, u+1cd0-1cf6, u+1cf8-1cf9, u+200c-200d, u+20a8, u+20b9, u+25cc, u+a830-a839, u+a8e0-a8fb;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 700;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLCz7Z1JlFc-K.woff2) format('woff2');
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf, u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-display: swap;
  font-family: Poppins;
  font-style: normal;
  font-weight: 700;
  src: url(https://static.weeecdn.net/common/enki-fonts/en/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2) format('woff2');
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+2000-206f, u+2074, u+20ac, u+2122,
    u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd;
}
:root {
--f-fm-ltn-expressive: 'Random Grotesque Standard Medium', SF Pro Text, SF Pro, Microsoft YaHei, PingFang SC, Roboto, Helvetica Neue, Helvetica, Arial, Apple SD Gothic Neo, Malgun Gothic, BlinkMacSystemFont, -apple-system, Segoe UI, Ubuntu, sans-serif;
--f-fm-ltn: 'Poppins', SF Pro Text, SF Pro, Microsoft YaHei, PingFang SC, Roboto, Helvetica Neue, Helvetica, Arial, Apple SD Gothic Neo, Malgun Gothic, BlinkMacSystemFont, -apple-system, Segoe UI, Ubuntu, sans-serif;
--fd-5xl-sz: 72px;
--fd-5xl-wt: 500;
--fd-5xl-lh: 1;
--fd-5xl-wtsg: 600;
--fd-4xl-sz: 60px;
--fd-4xl-wt: 500;
--fd-4xl-lh: 1;
--fd-4xl-wtsg: 600;
--fd-3xl-sz: 48px;
--fd-3xl-wt: 500;
--fd-3xl-lh: 1;
--fd-3xl-wtsg: 600;
--fd-2xl-sz: 36px;
--fd-2xl-wt: 500;
--fd-2xl-lh: 1;
--fd-2xl-wtsg: 600;
--fd-xl-sz: 30px;
--fd-xl-wt: 500;
--fd-xl-lh: 1;
--fd-xl-wtsg: 600;
--fd-lg-sz: 24px;
--fd-lg-wt: 500;
--fd-lg-lh: 1;
--fd-lg-wtsg: 600;
--fd-sm-sz: 20px;
--fd-sm-wt: 500;
--fd-sm-lh: 1;
--fd-sm-wtsg: 600;
--fh-5xl-sz: 60px;
--fh-5xl-wt: 500;
--fh-5xl-lh: 1.10;
--fh-5xl-wtsg: 600;
--fh-4xl-sz: 48px;
--fh-4xl-wt: 500;
--fh-4xl-lh: 1.10;
--fh-4xl-wtsg: 600;
--fh-3xl-sz: 36px;
--fh-3xl-wt: 500;
--fh-3xl-lh: 1.10;
--fh-3xl-wtsg: 600;
--fh-2xl-sz: 30px;
--fh-2xl-wt: 500;
--fh-2xl-lh: 1.10;
--fh-2xl-wtsg: 600;
--fh-xl-sz: 24px;
--fh-xl-wt: 500;
--fh-xl-lh: 1.10;
--fh-xl-wtsg: 500;
--fh-lg-sz: 20px;
--fh-lg-wt: 500;
--fh-lg-lh: 1.10;
--fh-lg-wtsg: 600;
--fh-sm-sz: 18px;
--fh-sm-wt: 500;
--fh-sm-lh: 1.10;
--fh-sm-wtsg: 600;
--fb-2xl-sz: 24px;
--fb-2xl-wt: 400;
--fb-2xl-lh: 1.25;
--fb-2xl-wtsg: 600;
--fb-2xl-wtmd: 500;
--fb-xl-sz: 20px;
--fb-xl-wt: 400;
--fb-xl-lh: 1.25;
--fb-xl-wtsg: 600;
--fb-xl-wtmd: 500;
--fb-lg-sz: 18px;
--fb-lg-wt: 400;
--fb-lg-lh: 1.25;
--fb-lg-wtsg: 600;
--fb-lg-wtmd: 500;
--fb-bs-sz: 16px;
--fb-bs-wt: 400;
--fb-bs-lh: 1.25;
--fb-bs-wtsg: 600;
--fb-bs-wtmd: 500;
--fb-sm-sz: 14px;
--fb-sm-wt: 400;
--fb-sm-lh: 1.25;
--fb-sm-wtsg: 600;
--fb-sm-wtmd: 500;
--fb-xs-sz: 13px;
--fb-xs-wt: 400;
--fb-xs-lh: 1.25;
--fb-xs-wtsg: 600;
--fb-xs-wtmd: 500;
--fb-2xs-sz: 12px;
--fb-2xs-wt: 400;
--fb-2xs-lh: 1.25;
--fb-2xs-wtsg: 600;
--fb-2xs-wtmd: 500;
--fb-3xs-sz: 11px;
--fb-3xs-wt: 400;
--fb-3xs-lh: 1.25;
--fb-3xs-wtsg: 600;
--fb-3xs-wtmd: 500;
}
body, html { font-family: var(--f-fm-ltn); -webkit-text-size-adjust: 100%; -moz-osx-font-smoothing: grayscale; }