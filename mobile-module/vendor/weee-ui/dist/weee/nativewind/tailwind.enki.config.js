
    /**
     * Do not edit directly if you aren't in ~weee-ui/src/nativewind/source/config.js
     * Autogenerated on Mon Aug 18 2025 17:42:02 GMT+0800 (China Standard Time), edit in ~weee-ui/src/nativewind/source/config.js
     * IMPORTANT: React Native compatible configuration for NativeWind
     */
    

const plugin = require("tailwindcss/plugin");

const colors = require("./styles/color.tailwind");
const size = require("./styles/size.tailwind");
const font = require("./styles/font.tailwind");

const fontClasses = require("./lib/font-classes.tailwind");
const buttonClasses = require("./lib/button-classes.tailwind");

// React Native 兼容的 NativeWind 配置
const enkiNativeWindConfig = {
  theme: {
    extend: {
      colors: colors,
      borderRadius: size.radius,
      fontSize: font.size,
      fontWeight: font.weight,
      spacing: size.spacing,
      height: size.spacing,
      width: size.spacing,
      letterSpacing: font.tracking,
      lineHeight: {
        ...font.lineheight,
      },
    },
  },
  plugins: [
    plugin(function ({ addUtilities }) {
      addUtilities({
        ...fontClasses,
        ...buttonClasses,
        // 移除 elevation 和 filter classes，因为 React Native 不支持
      });
    }),
  ],
};

function withEnkiConfig(tailwindConfig = {}) {
  const defaultConfig = enkiNativeWindConfig;

  function mergeDeep(target, source) {
    const isObject = config => config && typeof config === 'object';

    for (const key in source) {
      const targetValue = target[key];
      const sourceValue = source[key];

      if (Array.isArray(targetValue) && Array.isArray(sourceValue)) {
        target[key] = [...targetValue, ...sourceValue];
      } else if (isObject(targetValue) && isObject(sourceValue)) {
        target[key] = mergeDeep({ ...targetValue }, sourceValue);
      } else {
        target[key] = sourceValue;
      }
    }

    return target;
  }

  return mergeDeep({ ...defaultConfig }, tailwindConfig);
}

module.exports = {
  withEnkiConfig,
};