
  /** 
   * Do not edit directly
   * Generated on Mon Aug 18 2025 17:42:09 GMT+0800 (China Standard Time)
   * React Native compatible button classes for NativeWind
   */
  

module.exports = {
  ".enki-button-primary": {
    "backgroundColor": "var(--color-btn-primary-bg)",
    "color": "var(--color-btn-primary-fg-default)"
  },
  ".enki-button-secondary": {
    "backgroundColor": "var(--color-btn-secondary-bg)",
    "color": "var(--color-btn-secondary-fg-default)"
  },
  ".enki-button-tertiary": {
    "backgroundColor": "var(--color-btn-tertiary-bg)",
    "color": "var(--color-btn-tertiary-fg-default)"
  },
  ".enki-button-confirmation": {
    "backgroundColor": "var(--color-btn-confirmation-bg)",
    "color": "var(--color-btn-confirmation-fg-default)"
  },
  ".enki-button-critical": {
    "backgroundColor": "var(--color-btn-critical-bg)",
    "color": "var(--color-btn-critical-fg-default)"
  },
  ".enki-button-disabled": {
    "backgroundColor": "var(--color-btn-disabled-bg)",
    "color": "var(--color-btn-disabled-fg-default)"
  }
}