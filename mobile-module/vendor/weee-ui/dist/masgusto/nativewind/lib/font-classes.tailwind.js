
  /** 
   * Do not edit directly
   * Generated on Mon Aug 18 2025 17:42:09 GMT+0800 (China Standard Time)
   * React Native compatible font classes for NativeWind
   */
  

module.exports = {
  ".enki-display-5xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-6xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-display-5xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-6xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-display-4xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-5xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-display-4xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-5xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-display-3xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-4xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-display-3xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-4xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-display-2xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-3xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-display-2xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-3xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-display-xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-2xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-display-xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-2xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-display-lg": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-display-lg-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-xl)",
    "fontFamily": "var(--font-family-cjk-display)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-display-sm": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-lg)",
    "fontFamily": "var(--font-family-cjk-display)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-display-sm-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-lg)",
    "fontFamily": "var(--font-family-cjk-display)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-heading-5xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-6xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-heading-5xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-6xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-heading-4xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-5xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-heading-4xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-5xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-heading-3xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-4xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-heading-3xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-4xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-heading-2xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-3xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-heading-2xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-3xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-heading-xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-2xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-heading-xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-2xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-heading-lg": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-heading-lg-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-xl)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-heading-sm": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-lg)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "fontWeight": "var(--font-weight-500-medium)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-heading-sm-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-lg)",
    "fontFamily": "var(--font-family-cjk-heading)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-body-2xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-2xl)",
    "fontFamily": "var(--font-family-cjk-body)",
    "fontWeight": "var(--font-weight-400-regular)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-body-2xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-2xl)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-body-2xl-medium": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-2xl)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-500-medium)"
  },
  ".enki-body-xl": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-xl)",
    "fontFamily": "var(--font-family-cjk-body)",
    "fontWeight": "var(--font-weight-400-regular)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-body-xl-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-xl)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-body-xl-medium": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-xl)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-500-medium)"
  },
  ".enki-body-lg": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-lg)",
    "fontFamily": "var(--font-family-cjk-body)",
    "fontWeight": "var(--font-weight-400-regular)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-body-lg-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-lg)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-body-lg-medium": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-lg)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-500-medium)"
  },
  ".enki-body-base": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-base)",
    "fontFamily": "var(--font-family-cjk-body)",
    "fontWeight": "var(--font-weight-400-regular)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-body-base-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-base)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-body-base-medium": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-base)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-500-medium)"
  },
  ".enki-body-sm": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-sm)",
    "fontFamily": "var(--font-family-cjk-body)",
    "fontWeight": "var(--font-weight-400-regular)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-body-sm-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-sm)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-body-sm-medium": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-sm)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-500-medium)"
  },
  ".enki-body-xs": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-xs)",
    "fontFamily": "var(--font-family-cjk-body)",
    "fontWeight": "var(--font-weight-400-regular)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-body-xs-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-xs)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-body-xs-medium": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-xs)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-500-medium)"
  },
  ".enki-body-2xs": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-2xs)",
    "fontFamily": "var(--font-family-cjk-body)",
    "fontWeight": "var(--font-weight-400-regular)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-body-2xs-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-2xs)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-body-2xs-medium": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-2xs)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-500-medium)"
  },
  ".enki-body-3xs": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-3xs)",
    "fontFamily": "var(--font-family-cjk-body)",
    "fontWeight": "var(--font-weight-400-regular)",
    "lineHeight": "var(--font-lineheight-125)"
  },
  ".enki-body-3xs-strong": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-3xs)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-700-bold)"
  },
  ".enki-body-3xs-medium": {
    "letterSpacing": "var(--font-tracking-widest)",
    "fontSize": "var(--font-size-3xs)",
    "fontFamily": "var(--font-family-cjk-body)",
    "lineHeight": "var(--font-lineheight-125)",
    "fontWeight": "var(--font-weight-500-medium)"
  }
}