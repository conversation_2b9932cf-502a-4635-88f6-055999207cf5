
//
// typography-cjk.swift
//

// Do not edit directly
// Generated on Mon, 18 Aug 2025 09:42:09 GMT


import UIKit

public class EnkiKitTypography {
    public static let fontTrackingWidest = 0.60
    public static let fontTrackingWider = 0.30
    public static let fontTrackingWide = 0.20
    public static let fontTrackingBase = 0
    public static let fontTrackingTight = -0.20
    public static let fontTrackingTighter = -0.30
    public static let fontTrackingTightest = -0.60
    public static let fontLineheight150 = 1.50
    public static let fontLineheight125 = 1.25
    public static let fontLineheight115 = 1.15
    public static let fontLineheight110 = 1.10
    public static let fontLineheight105 = 1.05
    public static let fontLineheight100 = 1
    public static let fontWeight800Extrabold = 800
    public static let fontWeight700Bold = 700
    public static let fontWeight600Semibold = 600
    public static let fontWeight500Medium = 500
    public static let fontWeight400Regular = 400
    public static let fontSize10xl = 170
    public static let fontSize9xl = 128
    public static let fontSize8xl = 96
    public static let fontSize7xl = 72
    public static let fontSize6xl = 60
    public static let fontSize5xl = 48
    public static let fontSize4xl = 36
    public static let fontSize3xl = 30
    public static let fontSize2xl = 24
    public static let fontSizeXl = 20
    public static let fontSizeLg = 18
    public static let fontSizeBase = 16
    public static let fontSizeSm = 14
    public static let fontSizeXs = 13
    public static let fontSize2xs = 12
    public static let fontSize3xs = 11
    public static let fontFamilyCjkDisplay = "System"
    public static let fontFamilyCjkHeading = "System"
    public static let fontFamilyCjkBody = "System"
    public static let fontFamilyCjkMain = "System"
    public static let fontCjkBody3xsWeightstrong = fontWeight700Bold
    public static let fontCjkBody3xsWeightmedium = fontWeight500Medium
    public static let fontCjkBody3xsWeight = fontWeight400Regular
    public static let fontCjkBody3xsFamily = fontFamilyCjkBody
    public static let fontCjkBody3xsLineheight = fontLineheight125
    public static let fontCjkBody3xsSize = fontSize3xs
    public static let fontCjkBody3xsTracking = fontTrackingWidest
    public static let fontCjkBody2xsWeightstrong = fontWeight700Bold
    public static let fontCjkBody2xsWeightmedium = fontWeight500Medium
    public static let fontCjkBody2xsWeight = fontWeight400Regular
    public static let fontCjkBody2xsFamily = fontFamilyCjkBody
    public static let fontCjkBody2xsLineheight = fontLineheight125
    public static let fontCjkBody2xsSize = fontSize2xs
    public static let fontCjkBody2xsTracking = fontTrackingWidest
    public static let fontCjkBodyXsWeightstrong = fontWeight700Bold
    public static let fontCjkBodyXsWeightmedium = fontWeight500Medium
    public static let fontCjkBodyXsWeight = fontWeight400Regular
    public static let fontCjkBodyXsFamily = fontFamilyCjkBody
    public static let fontCjkBodyXsLineheight = fontLineheight125
    public static let fontCjkBodyXsSize = fontSizeXs
    public static let fontCjkBodyXsTracking = fontTrackingWidest
    public static let fontCjkBodySmWeightstrong = fontWeight700Bold
    public static let fontCjkBodySmWeightmedium = fontWeight500Medium
    public static let fontCjkBodySmWeight = fontWeight400Regular
    public static let fontCjkBodySmFamily = fontFamilyCjkBody
    public static let fontCjkBodySmLineheight = fontLineheight125
    public static let fontCjkBodySmSize = fontSizeSm
    public static let fontCjkBodySmTracking = fontTrackingWidest
    public static let fontCjkBodyBaseWeightstrong = fontWeight700Bold
    public static let fontCjkBodyBaseWeightmedium = fontWeight500Medium
    public static let fontCjkBodyBaseWeight = fontWeight400Regular
    public static let fontCjkBodyBaseFamily = fontFamilyCjkBody
    public static let fontCjkBodyBaseLineheight = fontLineheight125
    public static let fontCjkBodyBaseSize = fontSizeBase
    public static let fontCjkBodyBaseTracking = fontTrackingWidest
    public static let fontCjkBodyLgWeightstrong = fontWeight700Bold
    public static let fontCjkBodyLgWeightmedium = fontWeight500Medium
    public static let fontCjkBodyLgWeight = fontWeight400Regular
    public static let fontCjkBodyLgFamily = fontFamilyCjkBody
    public static let fontCjkBodyLgLineheight = fontLineheight125
    public static let fontCjkBodyLgSize = fontSizeLg
    public static let fontCjkBodyLgTracking = fontTrackingWidest
    public static let fontCjkBodyXlWeightstrong = fontWeight700Bold
    public static let fontCjkBodyXlWeightmedium = fontWeight500Medium
    public static let fontCjkBodyXlWeight = fontWeight400Regular
    public static let fontCjkBodyXlFamily = fontFamilyCjkBody
    public static let fontCjkBodyXlLineheight = fontLineheight125
    public static let fontCjkBodyXlSize = fontSizeXl
    public static let fontCjkBodyXlTracking = fontTrackingWidest
    public static let fontCjkBody2xlWeightstrong = fontWeight700Bold
    public static let fontCjkBody2xlWeightmedium = fontWeight500Medium
    public static let fontCjkBody2xlWeight = fontWeight400Regular
    public static let fontCjkBody2xlFamily = fontFamilyCjkBody
    public static let fontCjkBody2xlLineheight = fontLineheight125
    public static let fontCjkBody2xlSize = fontSize2xl
    public static let fontCjkBody2xlTracking = fontTrackingWidest
    public static let fontCjkHeadingSmWeightstrong = fontWeight700Bold
    public static let fontCjkHeadingSmWeight = fontWeight500Medium
    public static let fontCjkHeadingSmFamily = fontFamilyCjkHeading
    public static let fontCjkHeadingSmLineheight = fontLineheight125
    public static let fontCjkHeadingSmSize = fontSizeLg
    public static let fontCjkHeadingSmTracking = fontTrackingWidest
    public static let fontCjkHeadingLgWeightstrong = fontWeight700Bold
    public static let fontCjkHeadingLgWeight = fontWeight500Medium
    public static let fontCjkHeadingLgFamily = fontFamilyCjkHeading
    public static let fontCjkHeadingLgLineheight = fontLineheight125
    public static let fontCjkHeadingLgSize = fontSizeXl
    public static let fontCjkHeadingLgTracking = fontTrackingWidest
    public static let fontCjkHeadingXlWeightstrong = fontWeight700Bold
    public static let fontCjkHeadingXlWeight = fontWeight500Medium
    public static let fontCjkHeadingXlFamily = fontFamilyCjkHeading
    public static let fontCjkHeadingXlLineheight = fontLineheight125
    public static let fontCjkHeadingXlSize = fontSize2xl
    public static let fontCjkHeadingXlTracking = fontTrackingWidest
    public static let fontCjkHeading2xlWeightstrong = fontWeight700Bold
    public static let fontCjkHeading2xlWeight = fontWeight500Medium
    public static let fontCjkHeading2xlFamily = fontFamilyCjkHeading
    public static let fontCjkHeading2xlLineheight = fontLineheight125
    public static let fontCjkHeading2xlSize = fontSize3xl
    public static let fontCjkHeading2xlTracking = fontTrackingWidest
    public static let fontCjkHeading3xlWeightstrong = fontWeight700Bold
    public static let fontCjkHeading3xlWeight = fontWeight500Medium
    public static let fontCjkHeading3xlFamily = fontFamilyCjkHeading
    public static let fontCjkHeading3xlLineheight = fontLineheight125
    public static let fontCjkHeading3xlSize = fontSize4xl
    public static let fontCjkHeading3xlTracking = fontTrackingWidest
    public static let fontCjkHeading4xlWeightstrong = fontWeight700Bold
    public static let fontCjkHeading4xlWeight = fontWeight500Medium
    public static let fontCjkHeading4xlFamily = fontFamilyCjkHeading
    public static let fontCjkHeading4xlLineheight = fontLineheight125
    public static let fontCjkHeading4xlSize = fontSize5xl
    public static let fontCjkHeading4xlTracking = fontTrackingWidest
    public static let fontCjkHeading5xlWeightstrong = fontWeight700Bold
    public static let fontCjkHeading5xlWeight = fontWeight500Medium
    public static let fontCjkHeading5xlFamily = fontFamilyCjkHeading
    public static let fontCjkHeading5xlLineheight = fontLineheight125
    public static let fontCjkHeading5xlSize = fontSize6xl
    public static let fontCjkHeading5xlTracking = fontTrackingWidest
    public static let fontCjkDisplaySmWeightstrong = fontWeight700Bold
    public static let fontCjkDisplaySmWeight = fontWeight500Medium
    public static let fontCjkDisplaySmFamily = fontFamilyCjkDisplay
    public static let fontCjkDisplaySmLineheight = fontLineheight125
    public static let fontCjkDisplaySmSize = fontSizeLg
    public static let fontCjkDisplaySmTracking = fontTrackingWidest
    public static let fontCjkDisplayLgWeightstrong = fontWeight700Bold
    public static let fontCjkDisplayLgWeight = fontWeight500Medium
    public static let fontCjkDisplayLgFamily = fontFamilyCjkDisplay
    public static let fontCjkDisplayLgLineheight = fontLineheight125
    public static let fontCjkDisplayLgSize = fontSizeXl
    public static let fontCjkDisplayLgTracking = fontTrackingWidest
    public static let fontCjkDisplayXlWeightstrong = fontWeight700Bold
    public static let fontCjkDisplayXlWeight = fontWeight500Medium
    public static let fontCjkDisplayXlFamily = fontFamilyCjkDisplay
    public static let fontCjkDisplayXlLineheight = fontLineheight125
    public static let fontCjkDisplayXlSize = fontSize2xl
    public static let fontCjkDisplayXlTracking = fontTrackingWidest
    public static let fontCjkDisplay2xlWeightstrong = fontWeight700Bold
    public static let fontCjkDisplay2xlWeight = fontWeight500Medium
    public static let fontCjkDisplay2xlFamily = fontFamilyCjkDisplay
    public static let fontCjkDisplay2xlLineheight = fontLineheight125
    public static let fontCjkDisplay2xlSize = fontSize3xl
    public static let fontCjkDisplay2xlTracking = fontTrackingWidest
    public static let fontCjkDisplay3xlWeightstrong = fontWeight700Bold
    public static let fontCjkDisplay3xlWeight = fontWeight500Medium
    public static let fontCjkDisplay3xlFamily = fontFamilyCjkDisplay
    public static let fontCjkDisplay3xlLineheight = fontLineheight125
    public static let fontCjkDisplay3xlSize = fontSize4xl
    public static let fontCjkDisplay3xlTracking = fontTrackingWidest
    public static let fontCjkDisplay4xlWeightstrong = fontWeight700Bold
    public static let fontCjkDisplay4xlWeight = fontWeight500Medium
    public static let fontCjkDisplay4xlFamily = fontFamilyCjkDisplay
    public static let fontCjkDisplay4xlLineheight = fontLineheight125
    public static let fontCjkDisplay4xlSize = fontSize5xl
    public static let fontCjkDisplay4xlTracking = fontTrackingWidest
    public static let fontCjkDisplay5xlWeightstrong = fontWeight700Bold
    public static let fontCjkDisplay5xlWeight = fontWeight500Medium
    public static let fontCjkDisplay5xlFamily = fontFamilyCjkDisplay
    public static let fontCjkDisplay5xlLineheight = fontLineheight125
    public static let fontCjkDisplay5xlSize = fontSize6xl
    public static let fontCjkDisplay5xlTracking = fontTrackingWidest
}
