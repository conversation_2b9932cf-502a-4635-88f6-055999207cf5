
//
// typography-latin.swift
//

// Do not edit directly
// Generated on Mon, 18 Aug 2025 09:30:25 GMT


import UIKit

public class EnkiKitTypography {
    public static let fontTrackingWidest = 0.60
    public static let fontTrackingWider = 0.30
    public static let fontTrackingWide = 0.20
    public static let fontTrackingBase = 0
    public static let fontTrackingTight = -0.20
    public static let fontTrackingTighter = -0.30
    public static let fontTrackingTightest = -0.60
    public static let fontLineheight150 = 1.50
    public static let fontLineheight125 = 1.25
    public static let fontLineheight115 = 1.15
    public static let fontLineheight110 = 1.10
    public static let fontLineheight105 = 1.05
    public static let fontLineheight100 = 1
    public static let fontWeight800Extrabold = 800
    public static let fontWeight700Bold = 700
    public static let fontWeight600Semibold = 600
    public static let fontWeight500Medium = 500
    public static let fontWeight400Regular = 400
    public static let fontSize10xl = 170
    public static let fontSize9xl = 128
    public static let fontSize8xl = 96
    public static let fontSize7xl = 72
    public static let fontSize6xl = 60
    public static let fontSize5xl = 48
    public static let fontSize4xl = 36
    public static let fontSize3xl = 30
    public static let fontSize2xl = 24
    public static let fontSizeXl = 20
    public static let fontSizeLg = 18
    public static let fontSizeBase = 16
    public static let fontSizeSm = 14
    public static let fontSizeXs = 13
    public static let fontSize2xs = 12
    public static let fontSize3xs = 11
    public static let fontFamilyLatinAlt2 = "Brownhill Script"
    public static let fontFamilyLatinAlt1 = "Playfair Display"
    public static let fontFamilyLatinExpressive = "Random Grotesque Standard Medium"
    public static let fontFamilyLatinMono = "monospace"
    public static let fontFamilyLatinMain = "Poppins"
    public static let fontLatinBody3xsWeightstrong = fontWeight600Semibold
    public static let fontLatinBody3xsWeightmedium = fontWeight500Medium
    public static let fontLatinBody3xsWeight = fontWeight400Regular
    public static let fontLatinBody3xsLineheight = fontLineheight125
    public static let fontLatinBody3xsSize = fontSize3xs
    public static let fontLatinBody3xsTracking = fontTrackingBase
    public static let fontLatinBody2xsWeightstrong = fontWeight600Semibold
    public static let fontLatinBody2xsWeightmedium = fontWeight500Medium
    public static let fontLatinBody2xsWeight = fontWeight400Regular
    public static let fontLatinBody2xsLineheight = fontLineheight125
    public static let fontLatinBody2xsSize = fontSize2xs
    public static let fontLatinBody2xsTracking = fontTrackingBase
    public static let fontLatinBodyXsWeightstrong = fontWeight600Semibold
    public static let fontLatinBodyXsWeightmedium = fontWeight500Medium
    public static let fontLatinBodyXsWeight = fontWeight400Regular
    public static let fontLatinBodyXsLineheight = fontLineheight125
    public static let fontLatinBodyXsSize = fontSizeXs
    public static let fontLatinBodyXsTracking = fontTrackingBase
    public static let fontLatinBodySmWeightstrong = fontWeight600Semibold
    public static let fontLatinBodySmWeightmedium = fontWeight500Medium
    public static let fontLatinBodySmWeight = fontWeight400Regular
    public static let fontLatinBodySmLineheight = fontLineheight125
    public static let fontLatinBodySmSize = fontSizeSm
    public static let fontLatinBodySmTracking = fontTrackingBase
    public static let fontLatinBodyBaseWeightstrong = fontWeight600Semibold
    public static let fontLatinBodyBaseWeightmedium = fontWeight500Medium
    public static let fontLatinBodyBaseWeight = fontWeight400Regular
    public static let fontLatinBodyBaseLineheight = fontLineheight125
    public static let fontLatinBodyBaseSize = fontSizeBase
    public static let fontLatinBodyBaseTracking = fontTrackingBase
    public static let fontLatinBodyLgWeightstrong = fontWeight600Semibold
    public static let fontLatinBodyLgWeightmedium = fontWeight500Medium
    public static let fontLatinBodyLgWeight = fontWeight400Regular
    public static let fontLatinBodyLgLineheight = fontLineheight125
    public static let fontLatinBodyLgSize = fontSizeLg
    public static let fontLatinBodyLgTracking = fontTrackingBase
    public static let fontLatinBodyXlWeightstrong = fontWeight600Semibold
    public static let fontLatinBodyXlWeightmedium = fontWeight500Medium
    public static let fontLatinBodyXlWeight = fontWeight400Regular
    public static let fontLatinBodyXlLineheight = fontLineheight125
    public static let fontLatinBodyXlSize = fontSizeXl
    public static let fontLatinBodyXlTracking = fontTrackingBase
    public static let fontLatinBody2xlWeightstrong = fontWeight600Semibold
    public static let fontLatinBody2xlWeightmedium = fontWeight500Medium
    public static let fontLatinBody2xlWeight = fontWeight400Regular
    public static let fontLatinBody2xlLineheight = fontLineheight125
    public static let fontLatinBody2xlSize = fontSize2xl
    public static let fontLatinBody2xlTracking = fontTrackingBase
    public static let fontLatinHeadingSmWeightstrong = fontWeight600Semibold
    public static let fontLatinHeadingSmWeight = fontWeight500Medium
    public static let fontLatinHeadingSmLineheight = fontLineheight110
    public static let fontLatinHeadingSmSize = fontSizeLg
    public static let fontLatinHeadingSmTracking = fontTrackingBase
    public static let fontLatinHeadingLgWeightstrong = fontWeight600Semibold
    public static let fontLatinHeadingLgWeight = fontWeight500Medium
    public static let fontLatinHeadingLgLineheight = fontLineheight110
    public static let fontLatinHeadingLgSize = fontSizeXl
    public static let fontLatinHeadingLgTracking = fontTrackingBase
    public static let fontLatinHeadingXlWeightstrong = fontWeight500Medium
    public static let fontLatinHeadingXlWeight = fontWeight500Medium
    public static let fontLatinHeadingXlLineheight = fontLineheight110
    public static let fontLatinHeadingXlSize = fontSize2xl
    public static let fontLatinHeadingXlTracking = fontTrackingBase
    public static let fontLatinHeading2xlWeightstrong = fontWeight600Semibold
    public static let fontLatinHeading2xlWeight = fontWeight500Medium
    public static let fontLatinHeading2xlLineheight = fontLineheight110
    public static let fontLatinHeading2xlSize = fontSize3xl
    public static let fontLatinHeading2xlTracking = fontTrackingBase
    public static let fontLatinHeading3xlWeightstrong = fontWeight600Semibold
    public static let fontLatinHeading3xlWeight = fontWeight500Medium
    public static let fontLatinHeading3xlLineheight = fontLineheight110
    public static let fontLatinHeading3xlSize = fontSize4xl
    public static let fontLatinHeading3xlTracking = fontTrackingBase
    public static let fontLatinHeading4xlWeightstrong = fontWeight600Semibold
    public static let fontLatinHeading4xlWeight = fontWeight500Medium
    public static let fontLatinHeading4xlLineheight = fontLineheight110
    public static let fontLatinHeading4xlSize = fontSize5xl
    public static let fontLatinHeading4xlTracking = fontTrackingBase
    public static let fontLatinHeading5xlWeightstrong = fontWeight600Semibold
    public static let fontLatinHeading5xlWeight = fontWeight500Medium
    public static let fontLatinHeading5xlLineheight = fontLineheight110
    public static let fontLatinHeading5xlSize = fontSize6xl
    public static let fontLatinHeading5xlTracking = fontTrackingBase
    public static let fontLatinDisplaySmWeightstrong = fontWeight600Semibold
    public static let fontLatinDisplaySmWeight = fontWeight500Medium
    public static let fontLatinDisplaySmLineheight = fontLineheight100
    public static let fontLatinDisplaySmSize = fontSizeXl
    public static let fontLatinDisplaySmTracking = fontTrackingBase
    public static let fontLatinDisplayLgWeightstrong = fontWeight600Semibold
    public static let fontLatinDisplayLgWeight = fontWeight500Medium
    public static let fontLatinDisplayLgLineheight = fontLineheight100
    public static let fontLatinDisplayLgSize = fontSize2xl
    public static let fontLatinDisplayLgTracking = fontTrackingBase
    public static let fontLatinDisplayXlWeightstrong = fontWeight600Semibold
    public static let fontLatinDisplayXlWeight = fontWeight500Medium
    public static let fontLatinDisplayXlLineheight = fontLineheight100
    public static let fontLatinDisplayXlSize = fontSize3xl
    public static let fontLatinDisplayXlTracking = fontTrackingBase
    public static let fontLatinDisplay2xlWeightstrong = fontWeight600Semibold
    public static let fontLatinDisplay2xlWeight = fontWeight500Medium
    public static let fontLatinDisplay2xlLineheight = fontLineheight100
    public static let fontLatinDisplay2xlSize = fontSize4xl
    public static let fontLatinDisplay2xlTracking = fontTrackingBase
    public static let fontLatinDisplay3xlWeightstrong = fontWeight600Semibold
    public static let fontLatinDisplay3xlWeight = fontWeight500Medium
    public static let fontLatinDisplay3xlLineheight = fontLineheight100
    public static let fontLatinDisplay3xlSize = fontSize5xl
    public static let fontLatinDisplay3xlTracking = fontTrackingBase
    public static let fontLatinDisplay4xlWeightstrong = fontWeight600Semibold
    public static let fontLatinDisplay4xlWeight = fontWeight500Medium
    public static let fontLatinDisplay4xlLineheight = fontLineheight100
    public static let fontLatinDisplay4xlSize = fontSize6xl
    public static let fontLatinDisplay4xlTracking = fontTrackingBase
    public static let fontLatinDisplay5xlWeightstrong = fontWeight600Semibold
    public static let fontLatinDisplay5xlWeight = fontWeight500Medium
    public static let fontLatinDisplay5xlLineheight = fontLineheight100
    public static let fontLatinDisplay5xlSize = fontSize7xl
    public static let fontLatinDisplay5xlTracking = fontTrackingBase
    public static let fontFamilyLatinDisplay = fontFamilyLatinExpressive
    public static let fontFamilyLatinHeading = fontFamilyLatinMain
    public static let fontFamilyLatinBody = fontFamilyLatinMain
    public static let fontLatinBody3xsFamily = fontFamilyLatinBody
    public static let fontLatinBody2xsFamily = fontFamilyLatinBody
    public static let fontLatinBodyXsFamily = fontFamilyLatinBody
    public static let fontLatinBodySmFamily = fontFamilyLatinBody
    public static let fontLatinBodyBaseFamily = fontFamilyLatinBody
    public static let fontLatinBodyLgFamily = fontFamilyLatinBody
    public static let fontLatinBodyXlFamily = fontFamilyLatinBody
    public static let fontLatinBody2xlFamily = fontFamilyLatinBody
    public static let fontLatinHeadingSmFamily = fontFamilyLatinHeading
    public static let fontLatinHeadingLgFamily = fontFamilyLatinHeading
    public static let fontLatinHeadingXlFamily = fontFamilyLatinHeading
    public static let fontLatinHeading2xlFamily = fontFamilyLatinHeading
    public static let fontLatinHeading3xlFamily = fontFamilyLatinHeading
    public static let fontLatinHeading4xlFamily = fontFamilyLatinHeading
    public static let fontLatinHeading5xlFamily = fontFamilyLatinHeading
    public static let fontLatinDisplaySmFamily = fontFamilyLatinDisplay
    public static let fontLatinDisplayLgFamily = fontFamilyLatinDisplay
    public static let fontLatinDisplayXlFamily = fontFamilyLatinDisplay
    public static let fontLatinDisplay2xlFamily = fontFamilyLatinDisplay
    public static let fontLatinDisplay3xlFamily = fontFamilyLatinDisplay
    public static let fontLatinDisplay4xlFamily = fontFamilyLatinDisplay
    public static let fontLatinDisplay5xlFamily = fontFamilyLatinDisplay
}
