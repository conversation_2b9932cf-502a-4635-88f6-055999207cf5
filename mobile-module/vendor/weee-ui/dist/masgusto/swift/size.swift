
//
// size.swift
//

// Do not edit directly
// Generated on Mon, 18 Aug 2025 09:30:25 GMT


import UIKit

public extension EnkiKitSize {
    static let sizeRadiusFull = 9999.00
    static let sizeRadius800 = 28.00
    static let sizeRadius700 = 24.00
    static let sizeRadius600 = 20.00
    static let sizeRadius500 = 16.00
    static let sizeRadius400 = 12.00
    static let sizeRadius300 = 8.00
    static let sizeRadius200 = 4.00
    static let sizeRadius100 = 2.00
    static let sizeDeviceMobileSmW = 360.00
    static let sizeDeviceMobileLgW = 390.00
    static let sizeDeviceTabletSmW = 600.00
    static let sizeDeviceTabletMdW = 768.00
    static let sizeDeviceTabletLgW = 1100.00
    static let sizeDeviceDesktopXsW = 1280.00
    static let sizeDeviceDesktopSmW = 1440.00
    static let sizeDeviceDesktopMdW = 1600.00
    static let sizeDeviceDesktopLgW = 1920.00
    static let sizeDeviceDesktopXlW = 2000.00
    static let sizeSpacing2000 = 80.00
    static let sizeSpacing1900 = 76.00
    static let sizeSpacing1800 = 72.00
    static let sizeSpacing1700 = 68.00
    static let sizeSpacing1600 = 64.00
    static let sizeSpacing1500 = 60.00
    static let sizeSpacing1400 = 56.00
    static let sizeSpacing1300 = 52.00
    static let sizeSpacing1200 = 48.00
    static let sizeSpacing1100 = 44.00
    static let sizeSpacing1000 = 40.00
    static let sizeSpacing900 = 36.00
    static let sizeSpacing800 = 32.00
    static let sizeSpacing700 = 28.00
    static let sizeSpacing600 = 24.00
    static let sizeSpacing500 = 20.00
    static let sizeSpacing400 = 16.00
    static let sizeSpacing300 = 12.00
    static let sizeSpacing200 = 8.00
    static let sizeSpacing100 = 4.00
    static let sizeSpacing50 = 2.00
    static let sizeSpacing0 = 0.00
    static let sizeElevationSpread300 = 3.00
    static let sizeElevationSpread200 = 2.00
    static let sizeElevationSpread100 = 1.00
    static let sizeElevationBlur2200 = 80.00
    static let sizeElevationBlur1800 = 64.00
    static let sizeElevationBlur1400 = 48.00
    static let sizeElevationBlur1300 = 44.00
    static let sizeElevationBlur1200 = 40.00
    static let sizeElevationBlur1100 = 36.00
    static let sizeElevationBlur1000 = 32.00
    static let sizeElevationBlur900 = 28.00
    static let sizeElevationBlur800 = 24.00
    static let sizeElevationBlur700 = 20.00
    static let sizeElevationBlur600 = 16.00
    static let sizeElevationBlur500 = 12.00
    static let sizeElevationBlur400 = 8.00
    static let sizeElevationBlur300 = 6.00
    static let sizeElevationBlur200 = 4.00
    static let sizeElevationBlur100 = 0.00
    static let sizeElevationDistance1200 = 24.00
    static let sizeElevationDistance1100 = 22.00
    static let sizeElevationDistance1000 = 20.00
    static let sizeElevationDistance900 = 18.00
    static let sizeElevationDistance800 = 16.00
    static let sizeElevationDistance700 = 14.00
    static let sizeElevationDistance600 = 12.00
    static let sizeElevationDistance500 = 10.00
    static let sizeElevationDistance400 = 8.00
    static let sizeElevationDistance300 = 6.00
    static let sizeElevationDistance200 = 2.00
    static let sizeElevationDistance100 = 1.00
}
