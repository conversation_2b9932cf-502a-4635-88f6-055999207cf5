
  /** 
   * Do not edit directly
   * Generated on Mon Aug 18 2025 17:42:09 GMT+0800 (China Standard Time)
   */
  

module.exports = {
  ".enki-display-5xl": {
    "letterSpacing": "var(--display-5xl-tracking)",
    "fontSize": "var(--display-5xl-size)",
    "fontFamily": "var(--display-5xl-family)",
    "fontWeight": "var(--display-5xl-weight)",
    "lineHeight": "var(--display-5xl-lineheight)"
  },
  ".enki-display-5xl-strong": {
    "letterSpacing": "var(--display-5xl-tracking)",
    "fontSize": "var(--display-5xl-size)",
    "fontFamily": "var(--display-5xl-family)",
    "lineHeight": "var(--display-5xl-lineheight)",
    "fontWeight": "var(--display-5xl-weightstrong)"
  },
  ".enki-display-4xl": {
    "letterSpacing": "var(--display-4xl-tracking)",
    "fontSize": "var(--display-4xl-size)",
    "fontFamily": "var(--display-4xl-family)",
    "fontWeight": "var(--display-4xl-weight)",
    "lineHeight": "var(--display-4xl-lineheight)"
  },
  ".enki-display-4xl-strong": {
    "letterSpacing": "var(--display-4xl-tracking)",
    "fontSize": "var(--display-4xl-size)",
    "fontFamily": "var(--display-4xl-family)",
    "lineHeight": "var(--display-4xl-lineheight)",
    "fontWeight": "var(--display-4xl-weightstrong)"
  },
  ".enki-display-3xl": {
    "letterSpacing": "var(--display-3xl-tracking)",
    "fontSize": "var(--display-3xl-size)",
    "fontFamily": "var(--display-3xl-family)",
    "fontWeight": "var(--display-3xl-weight)",
    "lineHeight": "var(--display-3xl-lineheight)"
  },
  ".enki-display-3xl-strong": {
    "letterSpacing": "var(--display-3xl-tracking)",
    "fontSize": "var(--display-3xl-size)",
    "fontFamily": "var(--display-3xl-family)",
    "lineHeight": "var(--display-3xl-lineheight)",
    "fontWeight": "var(--display-3xl-weightstrong)"
  },
  ".enki-display-2xl": {
    "letterSpacing": "var(--display-2xl-tracking)",
    "fontSize": "var(--display-2xl-size)",
    "fontFamily": "var(--display-2xl-family)",
    "fontWeight": "var(--display-2xl-weight)",
    "lineHeight": "var(--display-2xl-lineheight)"
  },
  ".enki-display-2xl-strong": {
    "letterSpacing": "var(--display-2xl-tracking)",
    "fontSize": "var(--display-2xl-size)",
    "fontFamily": "var(--display-2xl-family)",
    "lineHeight": "var(--display-2xl-lineheight)",
    "fontWeight": "var(--display-2xl-weightstrong)"
  },
  ".enki-display-xl": {
    "letterSpacing": "var(--display-xl-tracking)",
    "fontSize": "var(--display-xl-size)",
    "fontFamily": "var(--display-xl-family)",
    "fontWeight": "var(--display-xl-weight)",
    "lineHeight": "var(--display-xl-lineheight)"
  },
  ".enki-display-xl-strong": {
    "letterSpacing": "var(--display-xl-tracking)",
    "fontSize": "var(--display-xl-size)",
    "fontFamily": "var(--display-xl-family)",
    "lineHeight": "var(--display-xl-lineheight)",
    "fontWeight": "var(--display-xl-weightstrong)"
  },
  ".enki-display-lg": {
    "letterSpacing": "var(--display-lg-tracking)",
    "fontSize": "var(--display-lg-size)",
    "fontFamily": "var(--display-lg-family)",
    "fontWeight": "var(--display-lg-weight)",
    "lineHeight": "var(--display-lg-lineheight)"
  },
  ".enki-display-lg-strong": {
    "letterSpacing": "var(--display-lg-tracking)",
    "fontSize": "var(--display-lg-size)",
    "fontFamily": "var(--display-lg-family)",
    "lineHeight": "var(--display-lg-lineheight)",
    "fontWeight": "var(--display-lg-weightstrong)"
  },
  ".enki-display-sm": {
    "letterSpacing": "var(--display-sm-tracking)",
    "fontSize": "var(--display-sm-size)",
    "fontFamily": "var(--display-sm-family)",
    "fontWeight": "var(--display-sm-weight)",
    "lineHeight": "var(--display-sm-lineheight)"
  },
  ".enki-display-sm-strong": {
    "letterSpacing": "var(--display-sm-tracking)",
    "fontSize": "var(--display-sm-size)",
    "fontFamily": "var(--display-sm-family)",
    "lineHeight": "var(--display-sm-lineheight)",
    "fontWeight": "var(--display-sm-weightstrong)"
  },
  ".enki-heading-5xl": {
    "letterSpacing": "var(--heading-5xl-tracking)",
    "fontSize": "var(--heading-5xl-size)",
    "fontFamily": "var(--heading-5xl-family)",
    "fontWeight": "var(--heading-5xl-weight)",
    "lineHeight": "var(--heading-5xl-lineheight)"
  },
  ".enki-heading-5xl-strong": {
    "letterSpacing": "var(--heading-5xl-tracking)",
    "fontSize": "var(--heading-5xl-size)",
    "fontFamily": "var(--heading-5xl-family)",
    "lineHeight": "var(--heading-5xl-lineheight)",
    "fontWeight": "var(--heading-5xl-weightstrong)"
  },
  ".enki-heading-4xl": {
    "letterSpacing": "var(--heading-4xl-tracking)",
    "fontSize": "var(--heading-4xl-size)",
    "fontFamily": "var(--heading-4xl-family)",
    "fontWeight": "var(--heading-4xl-weight)",
    "lineHeight": "var(--heading-4xl-lineheight)"
  },
  ".enki-heading-4xl-strong": {
    "letterSpacing": "var(--heading-4xl-tracking)",
    "fontSize": "var(--heading-4xl-size)",
    "fontFamily": "var(--heading-4xl-family)",
    "lineHeight": "var(--heading-4xl-lineheight)",
    "fontWeight": "var(--heading-4xl-weightstrong)"
  },
  ".enki-heading-3xl": {
    "letterSpacing": "var(--heading-3xl-tracking)",
    "fontSize": "var(--heading-3xl-size)",
    "fontFamily": "var(--heading-3xl-family)",
    "fontWeight": "var(--heading-3xl-weight)",
    "lineHeight": "var(--heading-3xl-lineheight)"
  },
  ".enki-heading-3xl-strong": {
    "letterSpacing": "var(--heading-3xl-tracking)",
    "fontSize": "var(--heading-3xl-size)",
    "fontFamily": "var(--heading-3xl-family)",
    "lineHeight": "var(--heading-3xl-lineheight)",
    "fontWeight": "var(--heading-3xl-weightstrong)"
  },
  ".enki-heading-2xl": {
    "letterSpacing": "var(--heading-2xl-tracking)",
    "fontSize": "var(--heading-2xl-size)",
    "fontFamily": "var(--heading-2xl-family)",
    "fontWeight": "var(--heading-2xl-weight)",
    "lineHeight": "var(--heading-2xl-lineheight)"
  },
  ".enki-heading-2xl-strong": {
    "letterSpacing": "var(--heading-2xl-tracking)",
    "fontSize": "var(--heading-2xl-size)",
    "fontFamily": "var(--heading-2xl-family)",
    "lineHeight": "var(--heading-2xl-lineheight)",
    "fontWeight": "var(--heading-2xl-weightstrong)"
  },
  ".enki-heading-xl": {
    "letterSpacing": "var(--heading-xl-tracking)",
    "fontSize": "var(--heading-xl-size)",
    "fontFamily": "var(--heading-xl-family)",
    "fontWeight": "var(--heading-xl-weight)",
    "lineHeight": "var(--heading-xl-lineheight)"
  },
  ".enki-heading-xl-strong": {
    "letterSpacing": "var(--heading-xl-tracking)",
    "fontSize": "var(--heading-xl-size)",
    "fontFamily": "var(--heading-xl-family)",
    "lineHeight": "var(--heading-xl-lineheight)",
    "fontWeight": "var(--heading-xl-weightstrong)"
  },
  ".enki-heading-lg": {
    "letterSpacing": "var(--heading-lg-tracking)",
    "fontSize": "var(--heading-lg-size)",
    "fontFamily": "var(--heading-lg-family)",
    "fontWeight": "var(--heading-lg-weight)",
    "lineHeight": "var(--heading-lg-lineheight)"
  },
  ".enki-heading-lg-strong": {
    "letterSpacing": "var(--heading-lg-tracking)",
    "fontSize": "var(--heading-lg-size)",
    "fontFamily": "var(--heading-lg-family)",
    "lineHeight": "var(--heading-lg-lineheight)",
    "fontWeight": "var(--heading-lg-weightstrong)"
  },
  ".enki-heading-sm": {
    "letterSpacing": "var(--heading-sm-tracking)",
    "fontSize": "var(--heading-sm-size)",
    "fontFamily": "var(--heading-sm-family)",
    "fontWeight": "var(--heading-sm-weight)",
    "lineHeight": "var(--heading-sm-lineheight)"
  },
  ".enki-heading-sm-strong": {
    "letterSpacing": "var(--heading-sm-tracking)",
    "fontSize": "var(--heading-sm-size)",
    "fontFamily": "var(--heading-sm-family)",
    "lineHeight": "var(--heading-sm-lineheight)",
    "fontWeight": "var(--heading-sm-weightstrong)"
  },
  ".enki-body-2xl": {
    "letterSpacing": "var(--body-2xl-tracking)",
    "fontSize": "var(--body-2xl-size)",
    "fontFamily": "var(--body-2xl-family)",
    "fontWeight": "var(--body-2xl-weight)",
    "lineHeight": "var(--body-2xl-lineheight)"
  },
  ".enki-body-2xl-strong": {
    "letterSpacing": "var(--body-2xl-tracking)",
    "fontSize": "var(--body-2xl-size)",
    "fontFamily": "var(--body-2xl-family)",
    "lineHeight": "var(--body-2xl-lineheight)",
    "fontWeight": "var(--body-2xl-weightstrong)"
  },
  ".enki-body-2xl-medium": {
    "letterSpacing": "var(--body-2xl-tracking)",
    "fontSize": "var(--body-2xl-size)",
    "fontFamily": "var(--body-2xl-family)",
    "lineHeight": "var(--body-2xl-lineheight)",
    "fontWeight": "var(--body-2xl-weightmedium)"
  },
  ".enki-body-xl": {
    "letterSpacing": "var(--body-xl-tracking)",
    "fontSize": "var(--body-xl-size)",
    "fontFamily": "var(--body-xl-family)",
    "fontWeight": "var(--body-xl-weight)",
    "lineHeight": "var(--body-xl-lineheight)"
  },
  ".enki-body-xl-strong": {
    "letterSpacing": "var(--body-xl-tracking)",
    "fontSize": "var(--body-xl-size)",
    "fontFamily": "var(--body-xl-family)",
    "lineHeight": "var(--body-xl-lineheight)",
    "fontWeight": "var(--body-xl-weightstrong)"
  },
  ".enki-body-xl-medium": {
    "letterSpacing": "var(--body-xl-tracking)",
    "fontSize": "var(--body-xl-size)",
    "fontFamily": "var(--body-xl-family)",
    "lineHeight": "var(--body-xl-lineheight)",
    "fontWeight": "var(--body-xl-weightmedium)"
  },
  ".enki-body-lg": {
    "letterSpacing": "var(--body-lg-tracking)",
    "fontSize": "var(--body-lg-size)",
    "fontFamily": "var(--body-lg-family)",
    "fontWeight": "var(--body-lg-weight)",
    "lineHeight": "var(--body-lg-lineheight)"
  },
  ".enki-body-lg-strong": {
    "letterSpacing": "var(--body-lg-tracking)",
    "fontSize": "var(--body-lg-size)",
    "fontFamily": "var(--body-lg-family)",
    "lineHeight": "var(--body-lg-lineheight)",
    "fontWeight": "var(--body-lg-weightstrong)"
  },
  ".enki-body-lg-medium": {
    "letterSpacing": "var(--body-lg-tracking)",
    "fontSize": "var(--body-lg-size)",
    "fontFamily": "var(--body-lg-family)",
    "lineHeight": "var(--body-lg-lineheight)",
    "fontWeight": "var(--body-lg-weightmedium)"
  },
  ".enki-body-base": {
    "letterSpacing": "var(--body-base-tracking)",
    "fontSize": "var(--body-base-size)",
    "fontFamily": "var(--body-base-family)",
    "fontWeight": "var(--body-base-weight)",
    "lineHeight": "var(--body-base-lineheight)"
  },
  ".enki-body-base-strong": {
    "letterSpacing": "var(--body-base-tracking)",
    "fontSize": "var(--body-base-size)",
    "fontFamily": "var(--body-base-family)",
    "lineHeight": "var(--body-base-lineheight)",
    "fontWeight": "var(--body-base-weightstrong)"
  },
  ".enki-body-base-medium": {
    "letterSpacing": "var(--body-base-tracking)",
    "fontSize": "var(--body-base-size)",
    "fontFamily": "var(--body-base-family)",
    "lineHeight": "var(--body-base-lineheight)",
    "fontWeight": "var(--body-base-weightmedium)"
  },
  ".enki-body-sm": {
    "letterSpacing": "var(--body-sm-tracking)",
    "fontSize": "var(--body-sm-size)",
    "fontFamily": "var(--body-sm-family)",
    "fontWeight": "var(--body-sm-weight)",
    "lineHeight": "var(--body-sm-lineheight)"
  },
  ".enki-body-sm-strong": {
    "letterSpacing": "var(--body-sm-tracking)",
    "fontSize": "var(--body-sm-size)",
    "fontFamily": "var(--body-sm-family)",
    "lineHeight": "var(--body-sm-lineheight)",
    "fontWeight": "var(--body-sm-weightstrong)"
  },
  ".enki-body-sm-medium": {
    "letterSpacing": "var(--body-sm-tracking)",
    "fontSize": "var(--body-sm-size)",
    "fontFamily": "var(--body-sm-family)",
    "lineHeight": "var(--body-sm-lineheight)",
    "fontWeight": "var(--body-sm-weightmedium)"
  },
  ".enki-body-xs": {
    "letterSpacing": "var(--body-xs-tracking)",
    "fontSize": "var(--body-xs-size)",
    "fontFamily": "var(--body-xs-family)",
    "fontWeight": "var(--body-xs-weight)",
    "lineHeight": "var(--body-xs-lineheight)"
  },
  ".enki-body-xs-strong": {
    "letterSpacing": "var(--body-xs-tracking)",
    "fontSize": "var(--body-xs-size)",
    "fontFamily": "var(--body-xs-family)",
    "lineHeight": "var(--body-xs-lineheight)",
    "fontWeight": "var(--body-xs-weightstrong)"
  },
  ".enki-body-xs-medium": {
    "letterSpacing": "var(--body-xs-tracking)",
    "fontSize": "var(--body-xs-size)",
    "fontFamily": "var(--body-xs-family)",
    "lineHeight": "var(--body-xs-lineheight)",
    "fontWeight": "var(--body-xs-weightmedium)"
  },
  ".enki-body-2xs": {
    "letterSpacing": "var(--body-2xs-tracking)",
    "fontSize": "var(--body-2xs-size)",
    "fontFamily": "var(--body-2xs-family)",
    "fontWeight": "var(--body-2xs-weight)",
    "lineHeight": "var(--body-2xs-lineheight)"
  },
  ".enki-body-2xs-strong": {
    "letterSpacing": "var(--body-2xs-tracking)",
    "fontSize": "var(--body-2xs-size)",
    "fontFamily": "var(--body-2xs-family)",
    "lineHeight": "var(--body-2xs-lineheight)",
    "fontWeight": "var(--body-2xs-weightstrong)"
  },
  ".enki-body-2xs-medium": {
    "letterSpacing": "var(--body-2xs-tracking)",
    "fontSize": "var(--body-2xs-size)",
    "fontFamily": "var(--body-2xs-family)",
    "lineHeight": "var(--body-2xs-lineheight)",
    "fontWeight": "var(--body-2xs-weightmedium)"
  },
  ".enki-body-3xs": {
    "letterSpacing": "var(--body-3xs-tracking)",
    "fontSize": "var(--body-3xs-size)",
    "fontFamily": "var(--body-3xs-family)",
    "fontWeight": "var(--body-3xs-weight)",
    "lineHeight": "var(--body-3xs-lineheight)"
  },
  ".enki-body-3xs-strong": {
    "letterSpacing": "var(--body-3xs-tracking)",
    "fontSize": "var(--body-3xs-size)",
    "fontFamily": "var(--body-3xs-family)",
    "lineHeight": "var(--body-3xs-lineheight)",
    "fontWeight": "var(--body-3xs-weightstrong)"
  },
  ".enki-body-3xs-medium": {
    "letterSpacing": "var(--body-3xs-tracking)",
    "fontSize": "var(--body-3xs-size)",
    "fontFamily": "var(--body-3xs-family)",
    "lineHeight": "var(--body-3xs-lineheight)",
    "fontWeight": "var(--body-3xs-weightmedium)"
  }
}