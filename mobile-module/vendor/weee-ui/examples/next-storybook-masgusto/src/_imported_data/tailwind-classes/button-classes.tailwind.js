
  /** 
   * Do not edit directly
   * Generated on Mon Aug 18 2025 17:30:26 GMT+0800 (China Standard Time)
   */
  

module.exports = {
  ".enki-button-primary": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "#36c25b",
    "color": "#ffffff",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "brightness(105%) saturate(105%)"
      }
    },
    "&:active": {
      "filter": "brightness(108%) saturate(108%)"
    }
  },
  ".enki-button-secondary": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "#1f4f2d",
    "color": "#ffffff",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "brightness(105%) saturate(105%)"
      }
    },
    "&:active": {
      "filter": "brightness(108%) saturate(108%)"
    }
  },
  ".enki-button-tertiary": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "#eef2fb",
    "color": "#07101a",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "brightness(95%) saturate(105%)"
      }
    },
    "&:active": {
      "filter": "brightness(92%) saturate(108%)"
    }
  },
  ".enki-button-confirmation": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "#36c25b",
    "color": "#ffffff",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "brightness(95%) saturate(105%)"
      }
    },
    "&:active": {
      "filter": "brightness(92%) saturate(108%)"
    }
  },
  ".enki-button-critical": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "#ec4143",
    "color": "#ffffff",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "brightness(95%) saturate(105%)"
      }
    },
    "&:active": {
      "filter": "brightness(92%) saturate(108%)"
    }
  },
  ".enki-button-disabled": {
    "appearance": "none",
    "outline": "none",
    "cursor": "not-allowed",
    "backgroundColor": "#dee4f3",
    "color": "#758296"
  }
}