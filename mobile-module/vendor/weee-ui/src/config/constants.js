// _______________________________________
// _______________________________________
// _______________________________________  
// Get args
const args = process.argv.slice(2).reduce((acc, arg) => {
  const [key, value] = arg.replace('--', '').split('=');
  acc[key] = value;
  return acc;
}, {});

// _______________________________________
// _______________________________________
// _______________________________________
// Export constants
module.exports = {
  OUTPUT_DIR: `./${args.OUTPUT_DIR}`,
  OUTPUT_DIR_NO_LEADING_SLASH: `${args.OUTPUT_DIR}`,

  ENABLE_LESS_EXPORT: false,

  LOCALES: [
    `us-en`,
    `us-es`,
    `us-jp`,
    `us-kr`,
    `us-vi`,
    `us-zh-sc`,
    `us-zh-tc`,
  ],
  WEB_I18N_FONT_LOCALES_MAP: {
    "us-en": `en`,
    "us-es": `es`,
    "us-jp": `ja`,
    "us-kr": `ko`,
    "us-vi": `vi`,
    "us-zh-sc": `zh`,
    "us-zh-tc": `zht`,
  },
  SIZE_BASE: 16,
  V1_TOKEN_ORIGIN: `${args.TOKEN_SOURCE_DIR}`,

  V2_LOCALES: [
    `latin`,
    `tall`,
    `cjk`,
  ],

  V2_TOKEN_ORIGIN: `../${args.TOKEN_SOURCE_DIR}`,
  V2_SOURCE_SUBDIRECTORY: `/_lib/`,
  V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH: `_lib/`,
  V2_TOKEN_FORMATTED_OUTPUT_DIR: `${args.TOKEN_SOURCE_DIR}dist/`,
  V2_CSS_OUTPUT_DIR: `${args.OUTPUT_DIR}css/`,
  V2_CSS_OUTPUT_MINIFIED_DIR: `${args.OUTPUT_DIR}css/min/`,
  V2_CSS_OUTPUT_HASHED_DIR: `${args.OUTPUT_DIR}css/hash/`,
  V2_ARCHIVE_SUBDIRECTORY: `/_source/`,

  V2_FONT_CSS_SOURCE_DIR: `src/platforms/web/font-css/`,
  V2_FONT_CSS_LATIN_FILE: `latin.css`,
  V2_FONT_CSS_TALL_FILE: `tall.css`,

  V2_CSS_BUNDLE_FILE_LATIN: `main.latin.css`,
  V2_CSS_BUNDLE_FILE_TALL: `main.tall.css`,
  V2_CSS_BUNDLE_FILE_CJK: `main.cjk.css`,
  
  V2_SCSS_OUTPUT_DIR: `${args.OUTPUT_DIR}scss/`,
  V2_SCSS_BUNDLE_FILENAME: `enki.scss`,

  V2_TAILWIND_OUTPUT_DIR: `${args.OUTPUT_DIR}tailwind/with-vars/`,
  V2_TAILWIND_STYLES_OUTPUT_DIR: `${args.OUTPUT_DIR}tailwind/with-vars/styles/`,
  V2_TAILWIND_CSS_OUTPUT_DIR: `${args.OUTPUT_DIR}tailwind/with-vars/css/`,
  V2_TAILWIND_CSS_OUTPUT_MINIFIED_DIR: `${args.OUTPUT_DIR}tailwind/with-vars/css/min/`,
  V2_TAILWIND_CSS_OUTPUT_HASHED_DIR: `${args.OUTPUT_DIR}tailwind/with-vars/css/hash/`,
  V2_TAILWIND_PLUGIN_JS_OUTPUT_DIR: `${args.OUTPUT_DIR}tailwind/with-vars/lib/`,
  V2_TAILWIND_CONFIG_FILENAME: `tailwind.enki.config.js`,
  V2_TAILWIND_CONFIG_SOURCE_PATH: `src/platforms/tailwind/config-source/`,
  V2_TAILWIND_TEMP_DIR: `./tmp/`,

  V2_TAILWIND_EMBEDDED_OUTPUT_DIR: `${args.OUTPUT_DIR}tailwind/embedded/`,
  V2_TAILWIND_EMBEDDED_STYLES_OUTPUT_DIR: `${args.OUTPUT_DIR}tailwind/embedded/styles/`,
  V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR: `${args.OUTPUT_DIR}tailwind/embedded/css/`,
  V2_TAILWIND_EMBEDDED_CSS_OUTPUT_MINIFIED_DIR: `${args.OUTPUT_DIR}tailwind/embedded/css/min/`,
  V2_TAILWIND_EMBEDDED_CSS_OUTPUT_HASHED_DIR: `${args.OUTPUT_DIR}tailwind/embedded/css/hash/`,
  V2_TAILWIND_EMBEDDED_PLUGIN_JS_OUTPUT_DIR: `${args.OUTPUT_DIR}tailwind/embedded/lib/`,
  V2_TAILWIND_EMBEDDED_CONFIG_FILENAME: `tailwind.enki.config.js`,
  V2_TAILWIND_EMBEDDED_CONFIG_SOURCE_PATH: `src/platforms/tailwind/config-source/`,

  V2_MINIFIED_DIR: `min/`,
  V2_HASHED_DIR: `min-hash/`,
  
  V2_TOKEN_FORMATTED_OUTPUT_DIR_BLOB: `${args.TOKEN_SOURCE_DIR}dist/**/!(*.dark).json`,
  V2_TOKEN_FORMATTED_OUTPUT_DIR_BLOB_DARKMODE: `${args.TOKEN_SOURCE_DIR}dist/**/*.dark.json`,

  V2_TOKEN_SOURCE_DIR_ALL_CORE_VARIABLES: `${args.TOKEN_SOURCE_DIR}dist/**/{colors.json,size.json,elevation.json,typography.core.json}`,
  V2_TOKEN_SOURCE_DIR_COLORS_WITH_ELEVATION: `${args.TOKEN_SOURCE_DIR}dist/**/{colors.json,elevation.json}`,
  V2_TOKEN_SOURCE_DIR_COLORS: `${args.TOKEN_SOURCE_DIR}dist/**/colors.json`,
  V2_TOKEN_SOURCE_DIR_COLORS_DARK: `${args.TOKEN_SOURCE_DIR}dist/**/colors.dark.json`,
  V2_TOKEN_SOURCE_DIR_COLORS_JSON: `${args.TOKEN_SOURCE_DIR}dist/colors.json`,

  V2_TOKEN_SOURCE_DIR_SIZE: `${args.TOKEN_SOURCE_DIR}dist/**/size.json`,
  V2_TOKEN_SOURCE_DIR_SIZE_WITH_ELEVATION: `${args.TOKEN_SOURCE_DIR}dist/**/{size.json,elevation.root.json}`,
  
  V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_CORE: `${args.TOKEN_SOURCE_DIR}dist/**/typography.core.json`,
  V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_LATIN: `${args.TOKEN_SOURCE_DIR}dist/**/{typography.latin.json,typography.core.json}`,
  V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_CJK: `${args.TOKEN_SOURCE_DIR}dist/**/{typography.cjk.json,typography.core.json}`,
  V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_TALL: `${args.TOKEN_SOURCE_DIR}dist/**/{typography.tall.json,typography.core.json}`,
  V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_COMPLETE_VARIABLES: `${args.TOKEN_SOURCE_DIR}dist/**/{typography.core.json,typography.all.json}`,
  V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_ALL_JSON: `${args.TOKEN_SOURCE_DIR}dist/typography.all.json`,

  V2_TOKEN_SOURCE_DIR_BUNDLE_CJK: `${args.TOKEN_SOURCE_DIR}dist/**/{colors.json,elevation.json,size.json,typography.cjk.json,typography.core.json}`,
  V2_TOKEN_SOURCE_DIR_BUNDLE_LATIN: `${args.TOKEN_SOURCE_DIR}dist/**/{colors.json,elevation.json,size.json,typography.latin.json,typography.core.json}`,
  V2_TOKEN_SOURCE_DIR_BUNDLE_TALL: `${args.TOKEN_SOURCE_DIR}dist/**/{colors.json,elevation.json,size.json,typography.tall.json,typography.core.json}`,

  
  V2_NO_REF_DIRECTORY: `_lib-noref/`,
  
  V2_COLORS_FILENAME: `color`,
  V2_COLORS_DARK_FILENAME: `color.dark`,
  V2_SIZE_FILENAME: `size`,
  V2_ELEVATION_FILENAME: `elevation`,
  V2_TOKEN_SOURCE_DIR_ELEVATION_JSON: `${args.TOKEN_SOURCE_DIR}dist/elevation.json`,
  V2_FILTERS_FILENAME: `filters`,
  V2_BUTTONS_FILENAME: `buttons`,
  V2_BUTTONS_DISABLED_FILENAME: `buttons.disabled`,
  V2_TYPOGRAPHY_FILENAME: `typography`,
  V2_TYPOGRAPHY_CORE_FILENAME: `typography.core`,
  V2_TYPOGRAPHY_LATIN_FILENAME: `typography.latin`,
  V2_TYPOGRAPHY_CJK_FILENAME: `typography.cjk`,
  V2_TYPOGRAPHY_TALL_FILENAME: `typography.tall`,
  
  V2_CJK_BUNDLE_FILENAME: `styles.cjk`,
  V2_LATIN_BUNDLE_FILENAME: `styles.latin`,
  V2_TALL_BUNDLE_FILENAME: `styles.tall`,

  V2_CSS_COLORS_DARK_SELECTOR: `:root [data-enki-display='dark']`,
  V2_CSS_FONTS_SELECTOR: `data-enki-fonts`,

  V2_SWIFT_TYPOGRAPHY_CORE_SELECTOR: `EnkiKitTypography`,
  V2_SWIFT_TYPOGRAPHY_LATIN_SELECTOR: `EnkiKitTypographyLatin`,
  V2_SWIFT_TYPOGRAPHY_TALL_SELECTOR: `EnkiKitTypographyTall`,
  V2_SWIFT_TYPOGRAPHY_CJK_SELECTOR: `EnkiKitTypographyCJK`,
  V2_SWIFT_SIZE_SELECTOR: `EnkiKitSize`,
  V2_SWIFT_COLORS_SELECTOR: `EnkiKitColor`,
  V2_SWIFT_COLORS_DARK_SELECTOR: `EnkiKitColorDark`,

  V2_SWIFT_CJK_BUNDLE_SELECTOR: `EnkiKitCJK`,
  V2_SWIFT_LATIN_BUNDLE_SELECTOR: `EnkiKitLatin`,
  V2_SWIFT_TALL_BUNDLE_SELECTOR: `EnkiKitLatinTall`,

  V2_EXAMPLES_ROOT: `./examples/`,
  V2_STORYBOOK_DIR: `next-storybook`,

  V2_TAILWIND_CONFIG_ROOT_DIR: `enki-tailwind/`,
  V2_NEXTJS_DATA_DIR: `src/_imported_data/tailwind-classes/`,
  V2_NEXTJS_STYLES_DIR: `src/assets/styles/`,
  
  V2_EXAMPLE_NEXTJS_WITH_TAILWIND_DIR: `next-js-with-tailwind-with-vars/`,
  V2_EXAMPLE_NEXTJS_EMBEDDED_DIR: `next-js-with-tailwind-embedded/`,

  V2_ANDROID_OUTPUT_DIR: `${args.OUTPUT_DIR}android/`,
  V2_ANDROID_COLORS_FILENAME: `colors_default`,
  V2_ANDROID_DIMENS_FILENAME: `dimens_default`,
  V2_ANDROID_DIMENS_FONT_BASE_FILENAME: `font_base_styles`,

  V2_ANDROID_DIMENS_FONT_PROP_PREFIX: `font_prop_`,
  V2_ANDROID_DIMENS_FONT_PROP_CJK_FILENAME: `font_prop_cjk`,
  V2_ANDROID_DIMENS_FONT_PROP_LATIN_FILENAME: `font_prop_latin`,
  V2_ANDROID_DIMENS_FONT_PROP_TALL_FILENAME: `font_prop_tall`,

  V2_ANDROID_FONT_FAMILY_CJK: false,
  V2_ANDROID_FONT_FAMILY_LATIN: `@font/poppins`,
  V2_ANDROID_FONT_FAMILY_TALL: `@font/be_vietnam_pro`,

  V2_ANDROID_FONT_FILES_ORIGIN: `src/platforms/android/fonts/`,
  V2_ANDROID_FONT_FILES_DIST: `fonts/`,

  // 在现有常量后添加 NativeWind 相关常量
  V2_NATIVEWIND_OUTPUT_DIR: `${args.OUTPUT_DIR}nativewind/`,
  V2_NATIVEWIND_STYLES_OUTPUT_DIR: `${args.OUTPUT_DIR}nativewind/styles/`,
  V2_NATIVEWIND_PLUGIN_JS_OUTPUT_DIR: `${args.OUTPUT_DIR}nativewind/lib/`,
  V2_NATIVEWIND_CONFIG_SOURCE_PATH: `src/platforms/nativewind/config-source/`,
  V2_NATIVEWIND_CONFIG_FILENAME: "tailwind.enki.config.js",
};
