const fs = require("fs");

function outputJsToFile(
  data,
  filename,
  outputDir
) {
  const path = `${outputDir}${filename}.tailwind.js`;

  console.log(`📥 Outputting tokens to '${path}'`);

  // Check if the file exists
  if (fs.existsSync(path)) {
    console.log(`💥 File '${path}' exists. Replacing file.`);
  } else {
    console.log(`🐣 File '${path}' does not exist. Creating new file.`);
  }

  // Write the nested object to the file
  fs.writeFile(path, data, (err) => {
    if (err) {
      console.error(`⛔️ Error writing to file '${path}'`, err);
    } else {
      console.log(`✅ File '${path}' has been written`);
    }
  });
}

module.exports = {
  outputJsToFile,
};