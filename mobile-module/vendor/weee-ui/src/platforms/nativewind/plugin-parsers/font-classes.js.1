const fs = require("fs");
const { cssToJs } = require("../../../utils/cssToJs");
const { cleanOutputDir } = require("../../../utils/cleanOutputDir");
const { outputJsToFile } = require("../../../utils/outputJsFiles");
const Constants = require("../../../config/constants");

// React Native 兼容的字体类生成器
function filterReactNativeCompatibleStyles(styles) {
  const compatibleStyles = {};
  
  Object.keys(styles).forEach(className => {
    const style = styles[className];
    const filteredStyle = {};
    
    // 只保留 React Native 支持的样式属性
    Object.keys(style).forEach(prop => {
      const value = style[prop];
      
      // 跳过 CSS 变量
      if (typeof value === 'string' && value.includes('var(--')) {
        return;
      }
      
      // 映射 React Native 兼容的属性
      switch (prop) {
        case 'fontSize':
        case 'fontWeight':
        case 'fontFamily':
        case 'lineHeight':
        case 'letterSpacing':
          filteredStyle[prop] = value;
          break;
        default:
          // 跳过不兼容的属性
          break;
      }
    });
    
    if (Object.keys(filteredStyle).length > 0) {
      compatibleStyles[className] = filteredStyle;
    }
  });
  
  return compatibleStyles;
}

function generateNativeWindFontPluginClasses(
  outputDir = Constants.V2_NATIVEWIND_PLUGIN_JS_OUTPUT_DIR
) {
  const date = new Date();
  const fileHeader = `
  /** 
   * Do not edit directly
   * Generated on ${date}
   * React Native compatible font classes for NativeWind
   */
  `;
  const moduleHeader = `module.exports = `;

  cleanOutputDir(outputDir);

  fs.readFile(
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_FONTS_FILENAME}.css`,
    "utf8",
    function (err, data) {
      const rawStyles = cssToJs(data);
      const compatibleStyles = filterReactNativeCompatibleStyles(rawStyles);
      
      let fileData = `${fileHeader}\n\n${moduleHeader}${JSON.stringify(
        compatibleStyles,
        null,
        2
      )}`;

      console.log(`🪄 Creating NativeWind font style library → '${outputDir}'`);

      outputJsToFile(fileData, "font-classes", outputDir);
    }
  );
}

module.exports = {
  generateNativeWindFontPluginClasses,
};