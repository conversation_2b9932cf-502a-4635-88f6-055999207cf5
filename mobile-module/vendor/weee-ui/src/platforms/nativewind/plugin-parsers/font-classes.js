
const fs = require("fs");
const { cssToJs } = require("../../../utils/cssToJs");
const { cleanOutputDir } = require("../../../utils/cleanOutputDir");
const { outputJsToFile } = require("../../../utils/outputJsFiles");
const Constants = require("../../../config/constants");

// 解析 CSS 变量映射表
function parseCssVariables(cssContent) {
  const variables = {};
  const rootRegex = /:root\s*\{([^}]+)\}/g;
  const variableRegex = /--([^:]+):\s*([^;]+);/g;
  
  let rootMatch;
  while ((rootMatch = rootRegex.exec(cssContent)) !== null) {
    const rootContent = rootMatch[1];
    let variableMatch;
    while ((variableMatch = variableRegex.exec(rootContent)) !== null) {
      const varName = variableMatch[1].trim();
      const varValue = variableMatch[2].trim();
      variables[varName] = varValue;
    }
  }
  
  return variables;
}

// 解析 var() 函数并替换为实际值
function resolveVariableValue(value, variables) {
  if (typeof value !== 'string') return value;
  
  const varRegex = /var\(--([^)]+)\)/g;
  return value.replace(varRegex, (match, varName) => {
    const resolvedValue = variables[varName];
    if (resolvedValue) {
      // 递归解析嵌套的变量
      return resolveVariableValue(resolvedValue, variables);
    }
    // 如果找不到变量，返回默认值或原值
    return match;
  });
}

// React Native 兼容的字体类生成器
function filterReactNativeCompatibleStyles(styles, variables) {
  const compatibleStyles = {};
  
  Object.keys(styles).forEach(className => {
    const style = styles[className];
    const filteredStyle = {};
    
    // 只保留 React Native 支持的样式属性
    Object.keys(style).forEach(prop => {
      const value = style[prop];
      
      // 映射 React Native 兼容的属性
      switch (prop) {
        case 'fontSize':
        case 'fontWeight':
        case 'fontFamily':
        case 'lineHeight':
        case 'letterSpacing':
          // 解析 CSS 变量为实际值
          const resolvedValue = resolveVariableValue(value, variables);
          filteredStyle[prop] = resolvedValue;
          break;
        default:
          // 跳过不兼容的属性
          break;
      }
    });
    
    if (Object.keys(filteredStyle).length > 0) {
      compatibleStyles[className] = filteredStyle;
    }
  });
  
  return compatibleStyles;
}

function generateNativeWindFontPluginClasses(
  outputDir = Constants.V2_NATIVEWIND_PLUGIN_JS_OUTPUT_DIR
) {
  const date = new Date();
  const fileHeader = `
  /** 
   * Do not edit directly
   * Generated on ${date}
   * React Native compatible font classes for NativeWind
   */
  `;
  const moduleHeader = `module.exports = `;

  cleanOutputDir(outputDir);

  fs.readFile(
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_FONTS_FILENAME}.css`,
    "utf8",
    function (err, data) {
      if (err) {
        console.error(`⛔️ Error reading CSS file:`, err);
        return;
      }

      // 解析 CSS 变量
      const variables = parseCssVariables(data);
      console.log(`📋 Parsed ${Object.keys(variables).length} CSS variables`);
      
      // 转换 CSS 为 JS 对象
      const rawStyles = cssToJs(data);
      
      // 过滤并解析为 React Native 兼容的样式
      const compatibleStyles = filterReactNativeCompatibleStyles(rawStyles, variables);
      
      let fileData = `${fileHeader}\n\n${moduleHeader}${JSON.stringify(
        compatibleStyles,
        null,
        2
      )}`;

      console.log(`🪄 Creating NativeWind font style library → '${outputDir}'`);
      console.log(`✨ Generated ${Object.keys(compatibleStyles).length} compatible font classes`);

      outputJsToFile(fileData, "font-classes", outputDir);
    }
  );
}

module.exports = {
  generateNativeWindFontPluginClasses,
};

