const fs = require("fs");
const { cssToJs } = require("../../../utils/cssToJs");
const { cleanOutputDir } = require("../../../utils/cleanOutputDir");
const { outputJsToFile } = require("../../../utils/outputJsFiles");
const Constants = require("../../../config/constants");

// 解析 CSS 变量值
function parseCssVariables(cssContent) {
  const variables = {};
  const variableRegex = /--([\w-]+):\s*([^;]+);/g;
  let match;

  while ((match = variableRegex.exec(cssContent)) !== null) {
    variables[`--${match[1]}`] = match[2].trim();
  }

  return variables;
}

// 解析变量值
function resolveVariableValue(value, variables) {
  if (typeof value !== 'string' || !value.includes('var(--')) {
    return value;
  }

  const varMatch = value.match(/var\((--[\w-]+)\)/);
  if (varMatch && variables[varMatch[1]]) {
    return variables[varMatch[1]];
  }

  return value;
}

// React Native 兼容的按钮样式过滤器
function filterReactNativeCompatibleButtonStyles(styles, variables = {}) {
  const compatibleStyles = {};

  Object.keys(styles).forEach(className => {
    const style = styles[className];
    const filteredStyle = {};

    // 跳过媒体查询和伪类选择器
    if (className.includes('@media') || className.includes(':hover') || className.includes(':active')) {
      return;
    }

    Object.keys(style).forEach(prop => {
      const value = style[prop];

      // 映射 React Native 兼容的属性
      switch (prop) {
        case 'backgroundColor':
        case 'color':
          // 解析 CSS 变量为实际值
          const resolvedValue = resolveVariableValue(value, variables);
          filteredStyle[prop] = resolvedValue;
          break;
        case 'appearance':
        case 'outline':
        case 'cursor':
        case 'filter':
          // 跳过 React Native 不支持的属性
          break;
        default:
          // 其他属性根据需要添加
          break;
      }
    });

    if (Object.keys(filteredStyle).length > 0) {
      compatibleStyles[className] = filteredStyle;
    }
  });

  return compatibleStyles;
}

function generateNativeWindButtonPluginClasses(
  outputDir = Constants.V2_NATIVEWIND_PLUGIN_JS_OUTPUT_DIR
) {
  const date = new Date();
  const fileHeader = `
  /** 
   * Do not edit directly
   * Generated on ${date}
   * React Native compatible button classes for NativeWind
   */
  `;
  const moduleHeader = `module.exports = `;

  cleanOutputDir(outputDir);

  const cssFilePath = `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_BUTTONS_FILENAME}.css`;
  console.log(`📂 Reading CSS file from: ${cssFilePath}`);

  fs.readFile(
    cssFilePath,
    "utf8",
    function (err, data) {
      if (err) {
        console.error(`⛔️ Error reading CSS file for NativeWind button classes:`, err);
        // 创建空的导出文件
        let fileData = `${fileHeader}\n\n${moduleHeader}{}`;
        outputJsToFile(fileData, "button-classes", outputDir);
        return;
      }

      // 解析 CSS 变量
      const variables = parseCssVariables(data);
      console.log(`📋 Parsed ${Object.keys(variables).length} CSS variables for buttons`);

      // 转换 CSS 为 JS 对象
      const rawStyles = cssToJs(data);
      const compatibleStyles = filterReactNativeCompatibleButtonStyles(rawStyles, variables);

      console.log(`✨ Generated ${Object.keys(compatibleStyles).length} compatible button classes`);

      let fileData = `${fileHeader}\n\n${moduleHeader}${JSON.stringify(
        compatibleStyles,
        null,
        2
      )}`;

      console.log(`🪄 Creating NativeWind button style library → '${outputDir}'`);

      outputJsToFile(fileData, "button-classes", outputDir);
    }
  );
}

module.exports = {
  generateNativeWindButtonPluginClasses,
};