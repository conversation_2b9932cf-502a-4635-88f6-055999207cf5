const fs = require("fs");
const { cssToJs } = require("../../../utils/cssToJs");
const { cleanOutputDir } = require("../../../utils/cleanOutputDir");
const { outputJsToFile } = require("../../../utils/outputJsFiles");
const Constants = require("../../../config/constants");

// React Native 兼容的按钮样式过滤器
function filterReactNativeCompatibleButtonStyles(styles) {
  const compatibleStyles = {};
  
  Object.keys(styles).forEach(className => {
    const style = styles[className];
    const filteredStyle = {};
    
    Object.keys(style).forEach(prop => {
      const value = style[prop];
      
      // 跳过 CSS 变量和不兼容的属性
      if (typeof value === 'string' && value.includes('var(--')) {
        return;
      }
      
      // 映射 React Native 兼容的属性
      switch (prop) {
        case 'backgroundColor':
        case 'color':
          filteredStyle[prop] = value;
          break;
        case 'appearance':
        case 'outline':
        case 'cursor':
        case '@media(hover:hover)':
        case '&:hover':
        case '&:active':
        case 'filter':
          // 跳过 React Native 不支持的属性
          break;
        default:
          // 其他属性根据需要添加
          break;
      }
    });
    
    if (Object.keys(filteredStyle).length > 0) {
      compatibleStyles[className] = filteredStyle;
    }
  });
  
  return compatibleStyles;
}

function generateNativeWindButtonPluginClasses(
  outputDir = Constants.V2_NATIVEWIND_PLUGIN_JS_OUTPUT_DIR
) {
  const date = new Date();
  const fileHeader = `
  /** 
   * Do not edit directly
   * Generated on ${date}
   * React Native compatible button classes for NativeWind
   */
  `;
  const moduleHeader = `module.exports = `;

  cleanOutputDir(outputDir);

  fs.readFile(
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_BUTTONS_FILENAME}.css`,
    "utf8",
    function (err, data) {
      const rawStyles = cssToJs(data);
      const compatibleStyles = filterReactNativeCompatibleButtonStyles(rawStyles);
      
      let fileData = `${fileHeader}\n\n${moduleHeader}${JSON.stringify(
        compatibleStyles,
        null,
        2
      )}`;

      console.log(`🪄 Creating NativeWind button style library → '${outputDir}'`);

      outputJsToFile(fileData, "button-classes", outputDir);
    }
  );
}

module.exports = {
  generateNativeWindButtonPluginClasses,
};