const Constants = require("./config/constants");
const { minifyAndHashCssFiles } = require("./utils/minifyAndHashCssFiles");
const { mergeFiles } = require("./utils/mergeFiles");
const { stripCssFiles } = require("./utils/stripFromCss");
const { mergeRootSelectors } = require("./utils/mergeCssRootSelectors");
const { replaceInFiles } = require("./utils/replaceInFiles");
const {
  replaceCssVariablesInFiles,
} = require("./utils/replaceCssVariablesInFiles");
const { removeJsKeys } = require("./utils/removeJsKeys");
const {
  replaceAndRemoveFontVariables,
} = require("./utils/replaceAndRemoveFontVariables");
const {
  combineAndProcessScssFiles,
} = require("./utils/combineAndProcessScssFiles");
const { appendOrPrependCssFiles } = require("./utils/appendOrPrependCssFiles");
const { updateCssOrScssFilterValues } = require("./utils/updateCssOrScssFilterValues");
const { formatAndroidDimens } = require("./utils/formatAndroidDimens");
const { mergeAndReformatAndroidFontStyles } = require("./utils/mergeAndReformatAndroidFonts");
const { populateTailwindConfigFromJson } = require("./utils/populateTailwindConfigFromJson");

// _________________________________
// _________________________________
// _________________________________
// Directories to minify & hash
const directories = [
  `${Constants.V2_CSS_OUTPUT_DIR}`,
  `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}`,
];

// _______________________________________
// _______________________________________
// _______________________________________
// CSS fonts
const cssFonts = {
  latin: `${Constants.V2_FONT_CSS_SOURCE_DIR}${Constants.V2_FONT_CSS_LATIN_FILE}`,
  tall: `${Constants.V2_FONT_CSS_SOURCE_DIR}${Constants.V2_FONT_CSS_TALL_FILE}`,
};

// _________________________________
// _________________________________
// _________________________________
// Utilities

function stripJs() {
  console.log("");
  console.log("╔═══════════════════════════════════════════════╗");
  console.log("║ 🛀 [CLEANUP] Removing unused JS variables...  ║");
  console.log("╚═══════════════════════════════════════════════╝");

  const files = [
    `${Constants.V2_TAILWIND_EMBEDDED_PLUGIN_JS_OUTPUT_DIR}font-classes.tailwind.js`,
  ];

  const patternsToStrip = ["behavior", "fontFamily"];

  stripCssFiles(files, patternsToStrip);
}

function removeJsKeysFromFiles() {
  console.log("");
  console.log(
    "╔═══════════════════════════════════════════════════════════════════╗"
  );
  console.log(
    "║ 🛀 [CLEANUP] Removing unused JS keys from the Tailwind config...  ║"
  );
  console.log(
    "╚═══════════════════════════════════════════════════════════════════╝"
  );

  const files = [
    `${Constants.V2_TAILWIND_STYLES_OUTPUT_DIR}color.tailwind.js`,
    `${Constants.V2_TAILWIND_EMBEDDED_STYLES_OUTPUT_DIR}color.tailwind.js`,
  ];

  const keysToRemove = ["behavior"];

  removeJsKeys(files, keysToRemove);
}

function stripCss() {
  console.log("");
  console.log("╔════════════════════════════════════════════════╗");
  console.log("║ 🛀 [CLEANUP] Removing unused CSS variables...  ║");
  console.log("╚════════════════════════════════════════════════╝");

  const files = [
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_CJK}`,
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_CJK}`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
  ];

  const patternsToStrip = ["device", "behavior"];

  stripCssFiles(files, patternsToStrip);

  console.log("");
  console.log(
    "╔══════════════════════════════════════════════════════════════════╗"
  );
  console.log(
    "║ 🛀 [CLEANUP-TAILWIND-EMBEDDED] Removing unused CSS variables...  ║"
  );
  console.log(
    "╚══════════════════════════════════════════════════════════════════╝"
  );

  const tailwindEmbeddedFiles = [
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_CJK}`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
  ];

  const tailwindEmbeddedPatternsToStrip = [
    "--size",
    "color",
    "style-",
    "behavior",
    "elevation",
    "font-family-number-main",
    "font-family-cjk-body",
    "font-family-cjk-heading",
    "font-family-cjk-display",
    "font-family-latin-body",
    "font-family-latin-heading",
    "font-family-latin-display",
    "font-family-tall-body",
    "font-family-tall-heading",
    "font-family-tall-display",
    "lg-family:",
    "xl-family:",
    "sm-family:",
    "xs-family:",
    "base-family:",
  ];

  stripCssFiles(tailwindEmbeddedFiles, tailwindEmbeddedPatternsToStrip);
}

function mergeCssRoots() {
  console.log("");
  console.log("╔══════════════════════════════════════════╗");
  console.log("║ 🛀 [CLEANUP] Merging :root selectors...  ║");
  console.log("╚══════════════════════════════════════════╝");

  const files = [
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_CJK}`,
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_CJK}`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_CJK}`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
  ];

  mergeRootSelectors(files);
}

function mergeCss() {
  console.log("");
  console.log("╔════════════════════════════════════╗");
  console.log("║ 🛀 [CLEANUP] Merging CSS files...  ║");
  console.log("╚════════════════════════════════════╝");

  const cssMergeOutput = Constants.V2_CSS_OUTPUT_DIR;

  const cssToMerge = [
    {
      files: [
        `${cssMergeOutput}styles.cjk.css`,
        `${cssMergeOutput}fonts.cjk.css`,
        `${cssMergeOutput}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_BUTTONS_FILENAME}.css`,
        `${cssMergeOutput}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_ELEVATION_FILENAME}.css`,
      ],
      outputFile: `${Constants.V2_CSS_BUNDLE_FILE_CJK}`,
      archiveDir: `${Constants.OUTPUT_DIR_NO_LEADING_SLASH}css/${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}`,
    },
    {
      files: [
        `${cssMergeOutput}styles.latin.css`,
        `${cssMergeOutput}fonts.latin.css`,
        `${cssMergeOutput}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_BUTTONS_FILENAME}.css`,
        `${cssMergeOutput}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_ELEVATION_FILENAME}.css`,
      ],
      outputFile: `${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
      archiveDir: `${Constants.OUTPUT_DIR_NO_LEADING_SLASH}css/${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}`,
    },
    {
      files: [
        `${cssMergeOutput}styles.tall.css`,
        `${cssMergeOutput}fonts.tall.css`,
        `${cssMergeOutput}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_BUTTONS_FILENAME}.css`,
        `${cssMergeOutput}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_ELEVATION_FILENAME}.css`,
      ],
      outputFile: `${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
      archiveDir: `${Constants.OUTPUT_DIR_NO_LEADING_SLASH}css/${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}`,
    },
  ];

  cssToMerge.forEach((group) => {
    mergeFiles(group.files, cssMergeOutput, group.outputFile, group.archiveDir);
  });
}

function mergeScss() {
  console.log("");
  console.log("╔═════════════════════════════════════╗");
  console.log("║ 🛀 [CLEANUP] Merging SCSS files...  ║");
  console.log("╚═════════════════════════════════════╝");

  const files = [
    `${Constants.V2_SCSS_OUTPUT_DIR}styles.cjk.scss`,
    `${Constants.V2_SCSS_OUTPUT_DIR}styles.tall.scss`,
    `${Constants.V2_SCSS_OUTPUT_DIR}styles.latin.scss`,
  ];

  combineAndProcessScssFiles(
    files,
    `${Constants.V2_SCSS_OUTPUT_DIR}${Constants.V2_SCSS_BUNDLE_FILENAME}`
  );
}

// ______________________________
// Merge Tailwind files

function mergeTailwindCss(
  tailwindCssDir = Constants.V2_TAILWIND_CSS_OUTPUT_DIR
) {
  console.log("");
  console.log("╔═════════════════════════════════════════════╗");
  console.log("║ 🛀 [CLEANUP] Merging Tailwind CSS files...  ║");
  console.log("╚═════════════════════════════════════════════╝");

  const tailwindCssToMerge = [
    {
      files: [
        `${tailwindCssDir}styles.tall.css`,
        `${tailwindCssDir}fonts.tall.root.css`,
      ],
      outputFile: `${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
      archiveDir: `${tailwindCssDir}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}`,
    },
    {
      files: [
        `${tailwindCssDir}styles.latin.css`,
        `${tailwindCssDir}fonts.latin.root.css`,
      ],
      outputFile: `${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
      archiveDir: `${tailwindCssDir}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}`,
    },
    {
      files: [
        `${tailwindCssDir}styles.cjk.css`,
        `${tailwindCssDir}fonts.cjk.root.css`,
      ],
      outputFile: `${Constants.V2_CSS_BUNDLE_FILE_CJK}`,
      archiveDir: `${tailwindCssDir}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}`,
    },
  ];

  tailwindCssToMerge.forEach((group) => {
    mergeFiles(group.files, tailwindCssDir, group.outputFile, group.archiveDir);
  });
}

// ______________________________
// Add filter behavior variables
// I hate this function, should exist in the build process but Style Dictionary is being difficult
function updateCssFilterBehaviorVariables() {
  console.log("");
  console.log("╔══════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [WEB-CSS-SCSS] Updating filter behavior variables...  ║");
  console.log("╚══════════════════════════════════════════════════════════╝");

  const files = [
    `${Constants.V2_SCSS_OUTPUT_DIR}styles.cjk.scss`,
    `${Constants.V2_SCSS_OUTPUT_DIR}styles.tall.scss`,
    `${Constants.V2_SCSS_OUTPUT_DIR}styles.latin.scss`,
    `${Constants.V2_SCSS_OUTPUT_DIR}color.dark.scss`,
    `${Constants.V2_SCSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}color.scss`,
    `${Constants.V2_CSS_OUTPUT_DIR}styles.cjk.css`,
    `${Constants.V2_CSS_OUTPUT_DIR}styles.latin.css`,
    `${Constants.V2_CSS_OUTPUT_DIR}styles.tall.css`,
    `${Constants.V2_CSS_OUTPUT_DIR}color.dark.css`,
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}color.css`,
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_NO_REF_DIRECTORY}color.css`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}styles.cjk.css`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}styles.latin.css`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}styles.tall.css`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}color.dark.css`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}styles.cjk.css`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}styles.latin.css`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}styles.tall.css`
  ]

  files.forEach((file) => {
    console.log(`Updating filter behavior variables for '${file}'...`);
    updateCssOrScssFilterValues(file, `${Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR}colors.json`, `color-*-behavior`)
  })
}

// ______________________________
// Minify files
function minifyCss() {
  console.log("");
  console.log("╔════════════════════════════════════════════════╗");
  console.log("║ 📥 [WEB-CSS] Minifying & hashing CSS files...  ║");
  console.log("╚════════════════════════════════════════════════╝");

  const directories = [
    `${Constants.V2_CSS_OUTPUT_DIR}`,
    `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}`,
  ];

  minifyAndHashCssFiles(directories);
}

// ______________________________
// Populate CSS variables
function populateCssVariables() {
  console.log("");
  console.log(
    "╔═══════════════════════════════════════════════════════════════╗"
  );
  console.log(
    "║ 📥 [TAILWIND-EMBEDDED] Populating CSS variable references...  ║"
  );
  console.log(
    "╚═══════════════════════════════════════════════════════════════╝"
  );

  const files = [
    `${Constants.V2_TAILWIND_EMBEDDED_PLUGIN_JS_OUTPUT_DIR}elevation-classes.tailwind.js`,
    `${Constants.V2_TAILWIND_EMBEDDED_PLUGIN_JS_OUTPUT_DIR}button-classes.tailwind.js`,
  ];

  const cssVars = `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_NO_REF_DIRECTORY}${Constants.V2_COLORS_FILENAME}.css`;

  replaceCssVariablesInFiles(files, cssVars);
}

// ______________________________
// Merge font loaders into files
function mergeFontFaces() {
  console.log("");
  console.log("╔═══════════════════════════════════════════════════════════════╗");
  console.log("║ 🪢 [FONTS] Merging @font-face declarations into css files...  ║");
  console.log("╚═══════════════════════════════════════════════════════════════╝");

  const cssFontSource = {
    latin: `${Constants.V2_FONT_CSS_SOURCE_DIR}${Constants.V2_FONT_CSS_LATIN_FILE}`,
    tall: `${Constants.V2_FONT_CSS_SOURCE_DIR}${Constants.V2_FONT_CSS_TALL_FILE}`,
  };

  const filesToMerge = [
    // [fontSource, outputFile, [filesToMerge]],
    [
      cssFontSource.latin,
      [
        `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
        `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
        `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
      ],
    ],
    [
      cssFontSource.tall,
      [
        `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
        `${Constants.V2_TAILWIND_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
        `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
      ],
    ],
  ];

  filesToMerge.forEach((fileGroup) => {
    fileGroup[1].forEach((sourceFile) => {
      console.log("");
      console.log(`🔀 Merging '${sourceFile}' declarations into '${fileGroup[0]}'...`);
      
      appendOrPrependCssFiles(sourceFile, [fileGroup[0]], sourceFile);
    })
  })
}

// ______________________________
// Replace variable names with smaller names
function replaceCssVarWithTinyNames() {
  console.log("");
  console.log(
    "╔══════════════════════════════════════════════════════════════╗"
  );
  console.log(
    "║ 📥 [TAILWIND-EMBEDDED] Making smaller CSS variable names...  ║"
  );
  console.log(
    "╚══════════════════════════════════════════════════════════════╝"
  );

  const files = [
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_CJK}`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
    `${Constants.V2_TAILWIND_EMBEDDED_PLUGIN_JS_OUTPUT_DIR}font-classes.tailwind.js`,
    `${Constants.V2_TAILWIND_EMBEDDED_PLUGIN_JS_OUTPUT_DIR}button-classes.tailwind.js`,
    `${Constants.V2_TAILWIND_EMBEDDED_STYLES_OUTPUT_DIR}font.tailwind.js`,
  ];

  const patterns = [
    // [from, to]

    ["--font-", "--f-"],
    ["--heading-", "--fh-"],
    ["--display-", "--fd-"],
    ["--body-", "--fb-"],

    ["-tracking", "-tr"],
    ["-weight", "-wt"],
    ["-lineheight", "-lh"],

    ["-size:", "-sz:"],
    ["-size)", "-sz)"],
    ["-f-size-", "-f-sz-"],

    ["-medium)", "-md)"],
    ["-bold)", "-bd)"],
    ["-semibold)", "-sbd)"],
    ["-regular)", "-rg)"],

    ["-medium:", "-md:"],
    ["-bold:", "-bd:"],
    ["-semibold:", "-sbd:"],
    ["-regular:", "-rg:"],

    ["-heading)", "-fh)"],
    ["-heading:", "-fh:"],

    ["-display)", "-fh)"],
    ["-display:", "-fh:"],

    ["-body)", "-fb)"],
    ["-body:", "-fb:"],

    ["-main)", ")"],
    ["-main:", ":"],

    ["sm-family", "sm-fm"],
    ["xs-family", "xs-fm"],
    ["xl-family", "xl-fm"],
    ["lg-family", "lg-fm"],
    ["base-family", "bs-fm"],
    ["f-family", "f-fm"],

    ["tr-base", "tr-bs"],
    ["fb-base", "fb-bs"],

    ["-wtstrong", "-wtsg"],
    ["-wtmedium", "-wtmd"],

    ["-widest", "-wdst"],
    ["-wider", "-wdr"],
    ["-wide", "-wd"],

    ["-tightest", "-tgtst"],
    ["-tighter", "-tgtr"],
    ["-tight", "-tgt"],

    ["-number", "-nb"],

    ["-fm-latin", "-fm-ltn"],
  ];

  replaceInFiles(files, patterns);
}

// _____________________________________
// Populate tailwind config with deeper key resolution
// _____________________________________
function populateTailwindConfigWithDeeperKeyResolution() {
  console.log("");
  console.log("╔══════════════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-EMBEDDED] Populating tailwind config with deeper key resolution...  ║");
  console.log("╚══════════════════════════════════════════════════════════════════════════════════╝");

  let tokens = `${Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR}colors.json`;
  
  let embeddedFiles = [
    [
      `${Constants.V2_TAILWIND_EMBEDDED_STYLES_OUTPUT_DIR}color.tailwind.js`,
    ]
  ]
  populateTailwindConfigFromJson(embeddedFiles, tokens, false);

  let cssVarsFiles = [
    [
      `${Constants.V2_TAILWIND_STYLES_OUTPUT_DIR}color.tailwind.js`,
    ]
  ]
  populateTailwindConfigFromJson(cssVarsFiles, tokens, true, 'color');
}

// Populate referenced CSS variables with values and remove original declaration
function processCssFontVars() {
  const files = [
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_CJK}`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_LATIN}`,
    `${Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR}${Constants.V2_CSS_BUNDLE_FILE_TALL}`,
  ];

  replaceAndRemoveFontVariables(files);
}

// ______________________________
// Format Android dimensions
async function formatAndroidDimensSyntax() {
  const files = [
    `${Constants.V2_ANDROID_OUTPUT_DIR}${Constants.V2_ANDROID_DIMENS_FILENAME}.xml`,
    `${Constants.V2_ANDROID_OUTPUT_DIR}${Constants.V2_ANDROID_DIMENS_FONT_BASE_FILENAME}.xml`
  ]

  formatAndroidDimens(files);
}

// ______________________________
// Format Android dimensions
async function formatAndroidFontSyntax() {
  const styleFile = `${Constants.V2_ANDROID_OUTPUT_DIR}${Constants.V2_ANDROID_DIMENS_FONT_BASE_FILENAME}.xml`;

  const files = [
    // [propFile, "fontFamily", "id"]
    [`${Constants.V2_ANDROID_OUTPUT_DIR}${Constants.V2_ANDROID_DIMENS_FONT_PROP_CJK_FILENAME}.xml`, Constants.V2_ANDROID_FONT_FAMILY_CJK, "cjk"],
    [`${Constants.V2_ANDROID_OUTPUT_DIR}${Constants.V2_ANDROID_DIMENS_FONT_PROP_LATIN_FILENAME}.xml`, Constants.V2_ANDROID_FONT_FAMILY_LATIN, "latin"],
    [`${Constants.V2_ANDROID_OUTPUT_DIR}${Constants.V2_ANDROID_DIMENS_FONT_PROP_TALL_FILENAME}.xml`, Constants.V2_ANDROID_FONT_FAMILY_TALL, "tall"]
  ];

  for (const [propFile, fontFamily, id] of files) {
    console.log(`🚀 Processing ${id} font styles...`);
    try {
      await mergeAndReformatAndroidFontStyles(propFile, styleFile, fontFamily, id);
      console.log(`✅ Completed processing ${id} font styles.`);
    } catch (error) {
      console.error(`❌ Error processing ${id} font styles:`, error);
    }
  }
}

// _________________________________
// _________________________________
// _________________________________
// Let's go
async function init() {
  removeJsKeysFromFiles();

  // @TODO: Lazy timeouts... Should be promises

  // setTimeout(() => {
  //   updateCssFilterBehaviorVariables();
  // }, 500)

  setTimeout(() => {
    mergeCss();
    mergeScss();
    mergeTailwindCss();
    mergeTailwindCss(Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR);
  }, 1000);

  setTimeout(() => {
    stripCss();
    stripJs();
  }, 1500);

  setTimeout(() => mergeCssRoots(), 2000);

  setTimeout(() => processCssFontVars(), 2500);

  setTimeout(() => replaceCssVarWithTinyNames(), 3000);

  setTimeout(() => mergeFontFaces(), 3500)

  setTimeout(() => populateCssVariables(), 4000);

  setTimeout(() => minifyCss(), 4500);

  setTimeout(() => populateTailwindConfigWithDeeperKeyResolution(), 5000);

  try {
    await formatAndroidDimensSyntax();
    await formatAndroidFontSyntax();
    console.log('🎉 Android font styles formatting process completed successfully!');
  } catch (error) {
    console.error('❌ An error occurred during the formatting process:', error);
  }
}

init();
