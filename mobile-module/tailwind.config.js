/** @type {import('tailwindcss').Config} */

const {
  withEnkiConfig: withWeeeEnkiConfig
} = require('./vendor/weee-ui/dist/weee/nativewind/tailwind.enki.config.js');
const {
  withEnkiConfig: withLatinoEnkiConfig
} = require('./vendor/weee-ui/dist/masgusto/nativewind/tailwind.enki.config.js');

const enkiConfig = {
  weee: withWeeeEnkiConfig,
  latino: withLatinoEnkiConfig
};

const withTailwindConfig = enkiConfig[process.env.CHANNEL] || enkiConfig.weee;

module.exports = withTailwindConfig({
  content: [
    "./App.{js,jsx,ts,tsx}",
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {},
  },
  plugins: [],
});

